body {
    margin: 0;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.login-container {
    display: flex;
    width: 900px;
    background-color: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}

.login-left {
    flex: 1;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #fff;
    padding: 60px 40px;
}

.login-left h1 {
    margin: 0 0 20px 0;
}

.login-right {
    flex: 1;
    padding: 60px 40px;
}

.login-right h2 {
    margin-bottom: 30px;
}

.input-group {
    margin-bottom: 20px;
}

.input-group input {
    width: 100%;
    padding: 12px 15px;
    border-radius: 8px;
    border: 1px solid #ccc;
    outline: none;
}

button {
    width: 100%;
    padding: 12px;
    background: #667eea;
    border: none;
    color: #fff;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
}

button:hover {
    background: #5a67d8;
}
