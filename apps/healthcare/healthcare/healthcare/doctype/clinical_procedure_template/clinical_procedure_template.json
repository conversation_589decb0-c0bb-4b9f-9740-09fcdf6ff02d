{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:template", "creation": "2017-10-05 14:59:55.438359", "description": "Procedure Template", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["template", "medical_department", "description", "column_break_5", "disabled", "pre_op_nursing_checklist_template", "post_op_nursing_checklist_template", "billing_section", "column_break_ea5h", "link_existing_item", "item", "item_code", "item_group", "column_break_lhbf", "is_billable", "rate", "consumption_tab", "consumables", "consume_stock", "items", "sample_collection_tab", "sample_collection", "sample_uom", "sample_qty", "sample", "column_break_21", "sample_details", "change_in_item", "codification", "codification_table", "service_request_defaults_tab", "patient_care_type", "staff_role"], "fields": [{"fieldname": "template", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "label": "Template Name", "reqd": 1, "unique": 1}, {"depends_on": "eval:!doc.link_existing_item || !doc.__islocal", "fieldname": "item_code", "fieldtype": "Data", "label": "Item Code", "mandatory_depends_on": "eval:!doc.link_existing_item", "read_only_depends_on": "eval: !doc.__islocal"}, {"fieldname": "item_group", "fieldtype": "Link", "ignore_user_permissions": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Item Group", "options": "Item Group", "read_only_depends_on": "eval:doc.link_existing_item", "reqd": 1}, {"fieldname": "medical_department", "fieldtype": "Link", "label": "Medical Department", "options": "Medical Department"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"default": "0", "depends_on": "eval:!doc.link_existing_item || !doc.__islocal;", "fieldname": "is_billable", "fieldtype": "Check", "label": "Is Billable"}, {"depends_on": "eval:doc.is_billable && (!doc.link_existing_item || !doc.__islocal)", "fieldname": "rate", "fieldtype": "Float", "in_list_view": 1, "in_standard_filter": 1, "label": "Rate", "mandatory_depends_on": "eval:doc.is_billable && !doc.link_existing_item"}, {"fieldname": "description", "fieldtype": "Small Text", "ignore_xss_filter": 1, "label": "Description", "no_copy": 1, "reqd": 1}, {"default": "0", "fieldname": "consume_stock", "fieldtype": "Check", "label": "Allow Stock Consumption", "search_index": 1}, {"fieldname": "consumables", "fieldtype": "Section Break", "label": "Consumables"}, {"depends_on": "eval:doc.consume_stock == 1", "fieldname": "items", "fieldtype": "Table", "ignore_user_permissions": 1, "label": "Items", "options": "Clinical Procedure Item"}, {"fieldname": "sample_collection", "fieldtype": "Section Break", "label": "Sample Collection"}, {"fieldname": "sample", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "<PERSON><PERSON>", "options": "Lab Test Sample"}, {"fetch_from": "sample.sample_uom", "fieldname": "sample_uom", "fieldtype": "Data", "label": "Sample UOM", "read_only": 1}, {"fieldname": "sample_qty", "fieldtype": "Float", "label": "Quantity"}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"fieldname": "sample_details", "fieldtype": "Small Text", "ignore_xss_filter": 1, "label": "Collection Details"}, {"default": "0", "fieldname": "change_in_item", "fieldtype": "Check", "hidden": 1, "label": "Change In Item", "no_copy": 1, "print_hide": 1, "report_hide": 1}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"depends_on": "eval: !doc.__islocal || doc.link_existing_item", "fieldname": "item", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "<PERSON><PERSON>", "mandatory_depends_on": "eval:doc.link_existing_item", "no_copy": 1, "options": "<PERSON><PERSON>", "read_only_depends_on": "eval: !doc.__islocal"}, {"fieldname": "codification_table", "fieldtype": "Table", "label": "Medical Codes", "options": "Codification Table"}, {"collapsible": 1, "fieldname": "codification", "fieldtype": "Tab Break", "label": "Medical Coding"}, {"fieldname": "pre_op_nursing_checklist_template", "fieldtype": "Link", "label": "Pre-op Nursing Checklist Template", "options": "Nursing Checklist Template"}, {"fieldname": "post_op_nursing_checklist_template", "fieldtype": "Link", "label": "Post-op Nursing Checklist Template", "options": "Nursing Checklist Template"}, {"default": "0", "depends_on": "eval: doc.__islocal", "fieldname": "link_existing_item", "fieldtype": "Check", "label": "Link existing Item"}, {"fieldname": "billing_section", "fieldtype": "Section Break", "label": "Billing"}, {"fieldname": "column_break_ea5h", "fieldtype": "Column Break"}, {"fieldname": "column_break_lhbf", "fieldtype": "Column Break"}, {"fieldname": "consumption_tab", "fieldtype": "Tab Break", "label": "Stock Consumption"}, {"fieldname": "sample_collection_tab", "fieldtype": "Tab Break", "label": "Sample Collection"}, {"fieldname": "patient_care_type", "fieldtype": "Link", "label": "Patient Care Type", "options": "Patient Care Type"}, {"fieldname": "staff_role", "fieldtype": "Link", "label": "Staff Role", "options": "Role"}, {"fieldname": "service_request_defaults_tab", "fieldtype": "Tab Break", "label": "Service Request Defaults"}], "links": [], "modified": "2023-11-06 15:00:26.292044", "modified_by": "Administrator", "module": "Healthcare", "name": "Clinical Procedure Template", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Nursing User", "share": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "write": 1}], "restrict_to_domain": "Healthcare", "search_fields": "template", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "template", "track_changes": 1, "track_seen": 1}