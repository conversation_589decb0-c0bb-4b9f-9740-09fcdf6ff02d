{"actions": [], "allow_rename": 1, "creation": "2022-03-15 18:16:17.060579", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["request_name", "request_date", "column_break_3", "status", "url", "section_break_4", "request", "traceback", "column_break_6", "response"], "fields": [{"fieldname": "request_name", "fieldtype": "Data", "in_list_view": 1, "label": "Request Name", "read_only": 1}, {"default": "Now", "fieldname": "request_date", "fieldtype": "Datetime", "label": "Request Date", "read_only": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Requested\nGranted\nRevoked\nExpired\nDenied", "read_only": 1}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "request", "fieldtype": "Code", "label": "Request", "read_only": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "response", "fieldtype": "Code", "label": "Response", "read_only": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "url", "fieldtype": "Data", "label": "URL"}, {"fieldname": "traceback", "fieldtype": "Code", "label": "<PERSON><PERSON>", "read_only": 1}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2022-03-24 11:11:01.555036", "modified_by": "Administrator", "module": "Healthcare", "name": "ABDM Request", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1, "track_seen": 1}