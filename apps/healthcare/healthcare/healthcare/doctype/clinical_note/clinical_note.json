{"actions": [], "allow_rename": 1, "autoname": "format:{patient}-{reference_name}-{#####}", "creation": "2023-05-12 12:31:47.611942", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["patient", "gender", "blood_group", "clinical_note_type", "terms_and_conditions", "column_break_jw2e", "posting_date", "practitioner", "user", "note_section", "note", "reference_section", "reference_doc", "reference_name"], "fields": [{"fieldname": "patient", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Patient", "options": "Patient", "reqd": 1}, {"fetch_from": "patient.sex", "fieldname": "gender", "fieldtype": "Link", "label": "Gender", "options": "Gender", "read_only": 1}, {"fetch_from": "patient.blood_group", "fieldname": "blood_group", "fieldtype": "Data", "label": "Blood Group", "read_only": 1}, {"allow_in_quick_entry": 1, "fieldname": "note", "fieldtype": "Text Editor", "label": "Note"}, {"fieldname": "reference_section", "fieldtype": "Section Break", "label": "Reference"}, {"fieldname": "reference_doc", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Reference Doc", "options": "DocType", "read_only": 1}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "label": "Reference Name", "options": "reference_doc", "read_only": 1}, {"fieldname": "note_section", "fieldtype": "Section Break", "label": "Note"}, {"fieldname": "column_break_jw2e", "fieldtype": "Column Break"}, {"default": "now", "fieldname": "posting_date", "fieldtype": "Datetime", "label": "Posting Date"}, {"fieldname": "practitioner", "fieldtype": "Link", "label": "Practitioner", "options": "Healthcare Practitioner", "read_only": 1}, {"fetch_from": "practitioner.user_id", "fieldname": "user", "fieldtype": "Link", "label": "User", "options": "User", "read_only": 1}, {"fieldname": "clinical_note_type", "fieldtype": "Link", "label": "Clinical Note Type", "options": "Clinical Note Type"}, {"fieldname": "terms_and_conditions", "fieldtype": "Link", "label": "Terms and Conditions", "options": "Terms and Conditions"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-05-16 18:47:54.650316", "modified_by": "Administrator", "module": "Healthcare", "name": "Clinical Note", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}