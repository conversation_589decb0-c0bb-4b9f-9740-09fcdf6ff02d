{"actions": [], "beta": 0, "creation": "2017-10-05 16:15:10.876952", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "item_name", "qty", "barcode", "uom", "invoice_separately_as_consumables", "column_break_5", "batch_no", "conversion_factor", "stock_uom", "transfer_qty", "actual_qty"], "fields": [{"bold": 1, "columns": 3, "fieldname": "item_code", "fieldtype": "Link", "ignore_user_permissions": 1, "in_global_search": 1, "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>", "reqd": 1, "search_index": 1}, {"fieldname": "barcode", "fieldtype": "Data", "label": "Barcode"}, {"fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name", "read_only": 1}, {"fieldname": "qty", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "reqd": 1}, {"fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "options": "UOM", "reqd": 1}, {"default": "0", "fieldname": "invoice_separately_as_consumables", "fieldtype": "Check", "in_list_view": 1, "label": "Invoice Separately as Consumables"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "batch_no", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>"}, {"fieldname": "conversion_factor", "fieldtype": "Float", "label": "Conversion Factor", "read_only": 1}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "read_only": 1, "reqd": 1}, {"fieldname": "transfer_qty", "fieldtype": "Float", "label": "Transfer Qty", "read_only": 1}, {"fieldname": "actual_qty", "fieldtype": "Float", "label": "Actual Qty (at source/target)", "no_copy": 1, "print_hide": 1, "read_only": 1, "search_index": 1}], "istable": 1, "links": [], "modified": "2020-03-01 15:34:54.226722", "modified_by": "Administrator", "module": "Healthcare", "name": "Clinical Procedure Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "restrict_to_domain": "Healthcare", "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}