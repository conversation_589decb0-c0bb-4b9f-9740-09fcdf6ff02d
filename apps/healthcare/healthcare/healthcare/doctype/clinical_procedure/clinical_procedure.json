{"actions": [], "autoname": "naming_series:", "creation": "2017-04-07 12:52:43.542429", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "title", "appointment", "procedure_template", "column_break_30", "company", "section_break_6", "patient", "patient_name", "patient_sex", "patient_age", "inpatient_record", "notes", "column_break_7", "status", "practitioner", "practitioner_name", "medical_department", "service_unit", "start_date", "start_time", "sample", "consumables_section", "consume_stock", "warehouse", "items", "section_break_24", "invoice_separately_as_consumables", "consumption_invoiced", "consumable_total_amount", "column_break_27", "consumption_details", "medical_coding_section", "codification_table", "sb_refs", "service_request", "invoiced", "prescription", "amended_from"], "fields": [{"fetch_from": "patient.inpatient_record", "fieldname": "inpatient_record", "fieldtype": "Link", "label": "Inpatient Record", "options": "Inpatient Record", "read_only": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "HLC-CPR-.YYYY.-"}, {"fieldname": "appointment", "fieldtype": "Link", "in_standard_filter": 1, "label": "Appointment", "options": "Patient Appointment", "set_only_once": 1}, {"fieldname": "patient", "fieldtype": "Link", "in_standard_filter": 1, "label": "Patient", "options": "Patient", "reqd": 1}, {"fieldname": "patient_age", "fieldtype": "Data", "label": "Age", "read_only": 1}, {"fieldname": "patient_sex", "fieldtype": "Link", "label": "Gender", "options": "Gender", "read_only": 1, "set_only_once": 1}, {"fieldname": "prescription", "fieldtype": "Link", "hidden": 1, "label": "Procedure Prescription", "options": "Procedure Prescription", "read_only": 1}, {"fieldname": "medical_department", "fieldtype": "Link", "in_standard_filter": 1, "label": "Medical Department", "options": "Medical Department"}, {"fieldname": "practitioner", "fieldtype": "Link", "in_standard_filter": 1, "label": "Healthcare Practitioner", "options": "Healthcare Practitioner"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "procedure_template", "fieldtype": "Link", "in_list_view": 1, "label": "Procedure Template", "options": "Clinical Procedure Template", "reqd": 1}, {"fieldname": "service_unit", "fieldtype": "Link", "label": "Service Unit", "options": "Healthcare Service Unit", "set_only_once": 1}, {"fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "mandatory_depends_on": "eval: doc.consume_stock == 1", "options": "Warehouse"}, {"default": "Today", "fieldname": "start_date", "fieldtype": "Date", "label": "Start Date"}, {"fieldname": "start_time", "fieldtype": "Time", "label": "Start Time"}, {"fieldname": "sample", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "Sample Collection"}, {"default": "0", "fieldname": "invoiced", "fieldtype": "Check", "label": "Invoiced", "no_copy": 1, "read_only": 1}, {"fieldname": "notes", "fieldtype": "Small Text", "label": "Notes", "set_only_once": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"default": "0", "fieldname": "consume_stock", "fieldtype": "Check", "label": "Consume Stock"}, {"fieldname": "items", "fieldtype": "Table", "label": "Consumables", "options": "Clinical Procedure Item"}, {"default": "0", "fieldname": "invoice_separately_as_consumables", "fieldtype": "Check", "hidden": 1, "label": "Invoice Consumables Separately", "read_only": 1}, {"depends_on": "invoice_separately_as_consumables", "fieldname": "consumable_total_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Consumable Total Amount", "read_only": 1}, {"depends_on": "invoice_separately_as_consumables", "fieldname": "consumption_details", "fieldtype": "Small Text", "label": "Consumption Details"}, {"default": "0", "depends_on": "invoice_separately_as_consumables", "fieldname": "consumption_invoiced", "fieldtype": "Check", "hidden": 1, "label": "Consumption Invoiced", "read_only": 1}, {"depends_on": "eval:!doc.__islocal", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Draft\nSubmitted\nCancelled\nIn Progress\nCompleted\nPending", "read_only": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Clinical Procedure", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "consume_stock", "fieldname": "consumables_section", "fieldtype": "Section Break", "label": "Consumables"}, {"fieldname": "column_break_27", "fieldtype": "Column Break"}, {"fieldname": "section_break_24", "fieldtype": "Section Break"}, {"fieldname": "column_break_30", "fieldtype": "Column Break"}, {"fieldname": "section_break_6", "fieldtype": "Section Break"}, {"collapsible": 1, "fieldname": "sb_refs", "fieldtype": "Section Break", "label": "More Information"}, {"fieldname": "patient_name", "fieldtype": "Data", "label": "Patient Name", "read_only": 1}, {"fieldname": "practitioner_name", "fieldtype": "Data", "in_list_view": 1, "label": "Practitioner Name", "read_only": 1}, {"allow_on_submit": 1, "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "medical_coding_section", "fieldtype": "Section Break", "label": "Medical Coding"}, {"fieldname": "codification_table", "fieldtype": "Table", "label": "Medical Codes", "options": "Codification Table"}, {"fieldname": "service_request", "fieldtype": "Link", "label": "Service Request", "no_copy": 1, "options": "Service Request", "print_hide": 1, "read_only": 1}], "is_submittable": 1, "links": [{"link_doctype": "Nursing Task", "link_fieldname": "reference_name"}], "modified": "2023-04-06 04:08:43.588359", "modified_by": "Administrator", "module": "Healthcare", "name": "Clinical Procedure", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Nursing User", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "submit": 1, "write": 1}], "restrict_to_domain": "Healthcare", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}