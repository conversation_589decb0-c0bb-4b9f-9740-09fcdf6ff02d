{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:appointment_type", "creation": "2016-07-22 11:52:34.953019", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["appointment_type", "default_duration", "allow_booking_for", "column_break_jdo1", "color", "billing_tab", "price_list", "items", "medical_codes_tab", "codification_table"], "fields": [{"allow_in_quick_entry": 1, "fieldname": "appointment_type", "fieldtype": "Data", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Type", "reqd": 1, "translatable": 1, "unique": 1}, {"allow_in_quick_entry": 1, "bold": 1, "fieldname": "default_duration", "fieldtype": "Int", "in_filter": 1, "in_list_view": 1, "label": "De<PERSON>ult <PERSON> (In Minutes)"}, {"allow_in_quick_entry": 1, "fieldname": "color", "fieldtype": "Color", "in_list_view": 1, "label": "Color", "no_copy": 1, "report_hide": 1}, {"fieldname": "price_list", "fieldtype": "Link", "label": "Price List", "options": "Price List"}, {"fieldname": "items", "fieldtype": "Table", "label": "Appointment Type Service Items", "options": "Appointment Type Service Item"}, {"fieldname": "billing_tab", "fieldtype": "Tab Break", "label": "Billing"}, {"fieldname": "column_break_jdo1", "fieldtype": "Column Break"}, {"default": "Practitioner", "fieldname": "allow_booking_for", "fieldtype": "Select", "label": "Allow Booking For", "options": "Practitioner\nDepartment\nService Unit", "reqd": 1}, {"fieldname": "medical_codes_tab", "fieldtype": "Tab Break", "label": "Medical Codes"}, {"fieldname": "codification_table", "fieldtype": "Table", "label": "Medical Code", "options": "Codification Table"}], "grid_page_length": 50, "links": [], "modified": "2025-03-31 17:31:51.709349", "modified_by": "Administrator", "module": "Healthcare", "name": "Appointment Type", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Healthcare Administrator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "write": 1}], "quick_entry": 1, "restrict_to_domain": "Healthcare", "row_format": "Dynamic", "search_fields": "appointment_type", "sort_field": "modified", "sort_order": "DESC", "states": []}