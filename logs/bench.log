2025-08-16 11:58:22,852 DEBUG cd frappe-bench && python3 -m venv env
2025-08-16 11:58:26,341 DEBUG cd frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-08-16 11:58:28,453 DEBUG cd frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet wheel
2025-08-16 11:58:29,618 LOG Getting frappe
2025-08-16 11:58:29,618 DEBUG cd frappe-bench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-08-16 11:58:33,390 LOG Installing frappe
2025-08-16 11:58:33,392 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-08-16 11:59:07,086 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files
2025-08-16 11:59:27,599 DEBUG cd frappe-bench && bench build
2025-08-16 11:59:27,737 INFO /usr/local/bin/bench build
2025-08-16 11:59:49,290 LOG setting up backups
2025-08-16 11:59:49,309 LOG backups were set up
2025-08-16 11:59:49,310 INFO Bench frappe-bench initialized
2025-08-16 12:00:01,972 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-16 12:04:32,835 INFO /usr/local/bin/bench new-site erpnextapp
2025-08-16 12:05:03,270 INFO /usr/local/bin/bench new-site erpnextapp
2025-08-16 12:06:58,733 INFO /usr/local/bin/bench new-site erpnextapp
2025-08-16 12:09:01,538 INFO /usr/local/bin/bench get-app --branch version-15 erpnext
2025-08-16 12:09:01,930 LOG Getting erpnext
2025-08-16 12:09:01,930 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-08-16 12:09:06,085 LOG Installing erpnext
2025-08-16 12:09:06,085 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erpnext 
2025-08-16 12:09:19,244 DEBUG cd /home/<USER>/frappe-bench/apps/erpnext && yarn install --check-files
2025-08-16 12:09:19,879 DEBUG bench build --app erpnext
2025-08-16 12:09:19,998 INFO /usr/local/bin/bench build --app erpnext
2025-08-16 12:10:37,490 INFO /usr/local/bin/bench get-app hrms
2025-08-16 12:10:37,826 LOG Getting hrms
2025-08-16 12:10:37,826 DEBUG cd ./apps && git clone https://github.com/frappe/hrms.git  --depth 1 --origin upstream
2025-08-16 12:10:38,944 LOG Installing hrms
2025-08-16 12:10:38,944 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/hrms 
2025-08-16 12:10:41,028 DEBUG cd /home/<USER>/frappe-bench/apps/hrms && yarn install --check-files
2025-08-16 12:11:26,461 DEBUG bench build --app hrms
2025-08-16 12:11:26,599 INFO /usr/local/bin/bench build --app hrms
2025-08-16 12:13:10,513 INFO /usr/local/bin/bench --site erpnextapp install-app erpnext
2025-08-16 12:14:50,002 INFO /usr/local/bin/bench --site erpnextapp install-app hrms
2025-08-16 12:15:49,415 INFO /usr/local/bin/bench start
2025-08-16 12:15:49,665 INFO /usr/local/bin/bench serve --port 8000
2025-08-16 12:15:49,695 INFO /usr/local/bin/bench schedule
2025-08-16 12:15:49,696 INFO /usr/local/bin/bench worker
2025-08-16 12:15:49,705 INFO /usr/local/bin/bench watch
2025-08-16 12:17:11,435 INFO /usr/local/bin/bench use erpnextapp
2025-08-16 12:17:24,291 INFO /usr/local/bin/bench start
2025-08-16 12:17:24,508 INFO /usr/local/bin/bench serve --port 8000
2025-08-16 12:17:24,510 INFO /usr/local/bin/bench schedule
2025-08-16 12:17:24,536 INFO /usr/local/bin/bench worker
2025-08-16 12:17:24,541 INFO /usr/local/bin/bench watch
2025-08-16 18:00:02,391 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-17 00:00:03,430 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-17 06:00:02,107 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-17 09:31:36,682 INFO /usr/local/bin/bench new-app my_theme
2025-08-17 09:31:36,694 LOG creating new app my_theme
2025-08-17 09:32:41,172 LOG Installing my_theme
2025-08-17 09:32:41,175 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/my_theme 
2025-08-17 09:32:43,857 DEBUG bench build --app my_theme
2025-08-17 09:32:43,997 INFO /usr/local/bin/bench build --app my_theme
2025-08-17 09:33:49,935 INFO /usr/local/bin/bench --site erpnextapp install-app my_theme
2025-08-17 09:50:58,385 INFO /usr/local/bin/bench version
2025-08-17 09:51:10,201 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-17 09:52:17,274 INFO /usr/local/bin/bench --site erpnextapp migrate --dry-run
2025-08-17 09:52:55,825 INFO /usr/local/bin/bench doctor
2025-08-17 10:10:31,918 INFO /usr/local/bin/bench build --app my_theme
2025-08-17 10:11:30,445 INFO /usr/local/bin/bench clear-cache
2025-08-17 10:11:44,122 INFO /usr/local/bin/bench restart
2025-08-17 10:17:37,672 INFO /usr/local/bin/bench build
2025-08-17 10:18:37,718 INFO /usr/local/bin/bench restart
2025-08-17 10:22:51,542 INFO /usr/local/bin/bench --site your_site_name list-apps
2025-08-17 10:23:03,492 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-17 10:23:47,351 INFO /usr/local/bin/bench restart
2025-08-17 10:24:46,008 INFO /usr/local/bin/bench --site erpnextapp clear-cache
2025-08-17 10:24:53,373 INFO /usr/local/bin/bench --site erpnextapp clear-website-cache
2025-08-17 10:25:01,689 INFO /usr/local/bin/bench --site erpnextapp migrate
2025-08-17 10:25:42,507 INFO /usr/local/bin/bench build
2025-08-17 10:26:55,399 INFO /usr/local/bin/bench restart
2025-08-17 10:31:18,284 INFO /usr/local/bin/bench clear-cache
2025-08-17 10:31:29,617 INFO /usr/local/bin/bench restart
2025-08-17 10:31:41,370 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-17 10:32:00,155 INFO /usr/local/bin/bench doctor
2025-08-17 10:33:03,734 INFO /usr/local/bin/bench clear-cache
2025-08-17 10:33:04,892 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 10:33:05,861 INFO /usr/local/bin/bench restart
2025-08-17 10:33:06,212 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-17 10:38:39,777 INFO /usr/local/bin/bench clear-cache
2025-08-17 10:39:21,635 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 10:39:34,890 INFO /usr/local/bin/bench restart
2025-08-17 10:39:45,293 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-17 10:40:28,056 INFO /usr/local/bin/bench doctor
2025-08-17 10:41:01,081 INFO /usr/local/bin/bench clear-cache
2025-08-17 10:41:02,393 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 10:41:03,471 INFO /usr/local/bin/bench restart
2025-08-17 10:41:03,846 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-17 10:41:22,339 INFO /usr/local/bin/bench start --no-dev
2025-08-17 10:43:16,339 INFO /usr/local/bin/bench --site erpnextapp console
2025-08-17 10:44:08,129 INFO /usr/local/bin/bench start
2025-08-17 10:44:58,536 INFO /usr/local/bin/bench --site erpnextapp serve --port 8001
2025-08-17 10:49:03,188 INFO /usr/local/bin/bench restart
2025-08-17 10:50:51,355 INFO /usr/local/bin/bench clear-cache
2025-08-17 10:50:52,484 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 10:50:53,533 INFO /usr/local/bin/bench restart
2025-08-17 10:50:53,848 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-17 10:51:07,821 INFO /usr/local/bin/bench --site erpnextapp serve --port 8000 --debug
2025-08-17 10:51:25,828 INFO /usr/local/bin/bench --site erpnextapp serve --port 8000
2025-08-17 10:52:29,947 INFO /usr/local/bin/bench restart
2025-08-17 10:53:23,264 INFO /usr/local/bin/bench start
2025-08-17 10:53:23,525 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 10:53:23,534 INFO /usr/local/bin/bench schedule
2025-08-17 10:53:23,536 INFO /usr/local/bin/bench worker
2025-08-17 10:53:23,539 INFO /usr/local/bin/bench watch
2025-08-17 10:54:52,668 INFO /usr/local/bin/bench start --no-dev
2025-08-17 10:54:52,872 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 10:54:52,875 INFO /usr/local/bin/bench watch
2025-08-17 10:54:52,900 INFO /usr/local/bin/bench schedule
2025-08-17 10:54:52,906 INFO /usr/local/bin/bench worker
2025-08-17 10:56:03,015 INFO /usr/local/bin/bench worker --queue default
2025-08-17 10:56:03,019 INFO /usr/local/bin/bench schedule
2025-08-17 10:56:03,021 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 11:03:15,663 INFO /usr/local/bin/bench --site erpnextapp set-admin-password
2025-08-17 11:03:30,137 INFO /usr/local/bin/bench --site erpnextapp set-admin-password
2025-08-17 11:11:19,959 INFO /usr/local/bin/bench clear-cache
2025-08-17 11:11:54,944 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 11:14:12,910 INFO /usr/local/bin/bench restart
2025-08-17 11:16:05,405 INFO /usr/local/bin/bench clear-cache
2025-08-17 11:16:16,294 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 11:19:46,032 INFO /usr/local/bin/bench build
2025-08-17 11:21:31,318 INFO /usr/local/bin/bench restart
2025-08-17 11:27:56,931 INFO /usr/local/bin/bench clear-cache
2025-08-17 11:28:08,443 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 11:32:13,412 INFO /usr/local/bin/bench build
2025-08-17 11:34:48,593 INFO /usr/local/bin/bench --site erpnextapp clear-cache
2025-08-17 11:34:54,864 INFO /usr/local/bin/bench --site erpnextapp clear-website-cache
2025-08-17 11:35:08,038 INFO /usr/local/bin/bench restart
2025-08-17 11:35:32,748 INFO /usr/local/bin/bench --site erpnextapp migrate
2025-08-17 11:44:44,487 INFO /usr/local/bin/bench clear-cache
2025-08-17 11:44:52,481 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 11:45:01,976 INFO /usr/local/bin/bench restart
2025-08-17 11:45:45,270 INFO /usr/local/bin/bench start
2025-08-17 11:46:11,677 INFO /usr/local/bin/bench start
2025-08-17 11:46:11,913 INFO /usr/local/bin/bench worker
2025-08-17 11:46:11,918 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 11:46:11,920 INFO /usr/local/bin/bench schedule
2025-08-17 11:46:11,938 INFO /usr/local/bin/bench watch
2025-08-17 11:46:24,841 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 11:46:24,850 INFO /usr/local/bin/bench schedule
2025-08-17 11:46:24,854 INFO /usr/local/bin/bench worker --queue default
2025-08-17 11:46:37,503 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 11:58:45,239 INFO /usr/local/bin/bench clear-cache
2025-08-17 11:58:54,083 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 11:59:10,461 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:00:02,082 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-17 12:03:41,745 INFO /usr/local/bin/bench clear-cache
2025-08-17 12:03:51,621 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 12:04:08,851 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:05:32,157 INFO /usr/local/bin/bench clear-cache
2025-08-17 12:05:33,284 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 12:16:12,283 INFO /usr/local/bin/bench --site erpnextapp set-admin-password
2025-08-17 12:22:00,516 INFO /usr/local/bin/bench clear-cache
2025-08-17 12:22:01,860 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 12:22:19,121 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:22:42,418 INFO /usr/local/bin/bench execute frappe.utils.install.make_site_dirs
2025-08-17 12:22:51,551 INFO /usr/local/bin/bench --site all console
2025-08-17 12:35:10,599 INFO /usr/local/bin/bench clear-cache
2025-08-17 12:35:11,912 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 12:35:39,265 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:36:05,599 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:36:39,892 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:42:40,076 INFO /usr/local/bin/bench clear-cache
2025-08-17 12:42:41,159 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 12:42:59,572 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:52:27,122 INFO /usr/local/bin/bench clear-cache
2025-08-17 12:52:28,146 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 12:52:47,689 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:55:41,165 INFO /usr/local/bin/bench clear-cache
2025-08-17 12:55:54,562 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 12:56:05,151 INFO /usr/local/bin/bench restart
2025-08-17 12:56:25,245 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:57:22,951 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 12:59:36,415 INFO /usr/local/bin/bench --version
2025-08-17 12:59:48,863 INFO /usr/local/bin/bench start
2025-08-17 13:00:39,739 INFO /usr/local/bin/bench serve --port 8000
2025-08-17 13:03:01,590 INFO /usr/local/bin/bench clear-cache
2025-08-17 13:03:02,852 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 13:03:03,888 INFO /usr/local/bin/bench build
2025-08-17 13:04:21,615 INFO /usr/local/bin/bench clear-cache
2025-08-17 13:06:51,318 INFO /usr/local/bin/bench clear-cache
2025-08-17 13:06:52,449 INFO /usr/local/bin/bench clear-website-cache
2025-08-17 18:00:02,784 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-18 00:00:03,101 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-18 06:00:02,848 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-18 12:00:02,028 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-18 18:00:02,432 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-18 21:03:12,371 INFO /usr/local/bin/bench start
2025-08-18 21:16:52,841 INFO /usr/local/bin/bench start
2025-08-18 21:17:31,622 INFO /usr/local/bin/bench start
2025-08-18 21:19:28,393 INFO /usr/local/bin/bench start
2025-08-18 21:19:28,660 INFO /usr/local/bin/bench serve --port 8000
2025-08-18 21:19:28,667 INFO /usr/local/bin/bench schedule
2025-08-18 21:19:28,667 INFO /usr/local/bin/bench watch
2025-08-18 21:19:28,667 INFO /usr/local/bin/bench worker
2025-08-18 21:20:56,102 INFO /usr/local/bin/bench restart
2025-08-18 21:21:16,688 INFO /usr/local/bin/bench restart
2025-08-18 21:22:17,290 INFO /usr/local/bin/bench start
2025-08-18 21:22:17,511 INFO /usr/local/bin/bench serve --port 8000
2025-08-18 21:22:17,516 INFO /usr/local/bin/bench worker
2025-08-18 21:22:17,517 INFO /usr/local/bin/bench watch
2025-08-18 21:22:17,535 INFO /usr/local/bin/bench schedule
2025-08-18 21:22:59,691 INFO /usr/local/bin/bench start
2025-08-18 21:22:59,918 INFO /usr/local/bin/bench serve --port 8000
2025-08-18 21:22:59,923 INFO /usr/local/bin/bench watch
2025-08-18 21:22:59,930 INFO /usr/local/bin/bench worker
2025-08-18 21:22:59,954 INFO /usr/local/bin/bench schedule
2025-08-19 00:00:03,255 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-19 06:00:02,084 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-19 06:46:09,462 INFO /usr/local/bin/bench start
2025-08-19 06:46:09,846 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 06:46:09,862 INFO /usr/local/bin/bench schedule
2025-08-19 06:46:09,881 INFO /usr/local/bin/bench watch
2025-08-19 06:46:09,888 INFO /usr/local/bin/bench worker
2025-08-19 06:52:11,011 INFO /usr/local/bin/bench build
2025-08-19 06:53:19,400 INFO /usr/local/bin/bench start
2025-08-19 06:53:19,612 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 06:53:19,614 INFO /usr/local/bin/bench worker
2025-08-19 06:53:19,616 INFO /usr/local/bin/bench schedule
2025-08-19 06:53:19,616 INFO /usr/local/bin/bench watch
2025-08-19 07:09:11,735 INFO /usr/local/bin/bench clear-cache
2025-08-19 07:09:12,787 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:01:37,391 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:01:38,530 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:03:40,380 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:03:49,000 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:03:57,681 INFO /usr/local/bin/bench restart
2025-08-19 08:04:07,063 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:07:17,495 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:09:16,104 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:09:17,209 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:09:18,154 INFO /usr/local/bin/bench build --app my_theme
2025-08-19 08:10:36,456 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:10:49,755 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:13:42,406 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:13:43,522 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:18:17,856 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:18:31,575 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:20:39,447 INFO /usr/local/bin/bench start
2025-08-19 08:20:39,726 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:20:39,726 INFO /usr/local/bin/bench worker
2025-08-19 08:20:39,754 INFO /usr/local/bin/bench schedule
2025-08-19 08:20:39,756 INFO /usr/local/bin/bench watch
2025-08-19 08:21:17,015 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:21:17,035 INFO /usr/local/bin/bench schedule
2025-08-19 08:21:17,048 INFO /usr/local/bin/bench worker
2025-08-19 08:21:41,414 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:21:52,401 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:21:52,412 INFO /usr/local/bin/bench worker
2025-08-19 08:21:52,413 INFO /usr/local/bin/bench schedule
2025-08-19 08:24:35,318 INFO /usr/local/bin/bench build
2025-08-19 08:25:50,158 INFO /usr/local/bin/bench start
2025-08-19 08:28:15,140 INFO /usr/local/bin/bench install-app my_theme
2025-08-19 08:28:27,999 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:28:29,115 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:28:55,709 INFO /usr/local/bin/bench --site localhost get-app my_theme
2025-08-19 08:28:55,713 WARNING /usr/local/bin/bench --site localhost get-app my_theme executed with exit code 2
2025-08-19 08:29:07,155 INFO /usr/local/bin/bench list-apps
2025-08-19 08:29:42,351 INFO /usr/local/bin/bench --site erpnextapp install-app my_theme
2025-08-19 08:31:42,689 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:31:44,197 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:32:40,765 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:32:40,783 INFO /usr/local/bin/bench schedule
2025-08-19 08:32:40,795 INFO /usr/local/bin/bench worker
2025-08-19 08:33:04,519 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:36:47,670 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:36:48,845 INFO /usr/local/bin/bench clear-website-cache
2025-08-19 08:38:13,843 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:38:36,001 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:38:48,736 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:48:10,532 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:51:01,701 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:51:12,217 INFO /usr/local/bin/bench serve --port 8000
2025-08-19 08:53:31,521 INFO /usr/local/bin/bench build
2025-08-19 08:55:44,088 INFO /usr/local/bin/bench start
2025-08-19 08:57:04,412 INFO /usr/local/bin/bench --site site1.local uninstall-app my_theme
2025-08-19 08:57:16,270 INFO /usr/local/bin/bench --site erpnexapp uninstall-app my_theme
2025-08-19 08:57:23,223 INFO /usr/local/bin/bench --site erpnextapp uninstall-app my_theme
2025-08-19 08:58:18,198 INFO /usr/local/bin/bench get-app https://github.com/netmanthan/netmanthan_themes.git
2025-08-19 08:58:18,211 LOG Getting netmanthan_themes
2025-08-19 08:58:18,211 DEBUG cd ./apps && git clone https://github.com/netmanthan/netmanthan_themes.git  --depth 1 --origin upstream
2025-08-19 08:58:18,851 LOG Installing netmanthan_themes
2025-08-19 08:58:18,852 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/netmanthan_themes 
2025-08-19 08:58:21,604 DEBUG bench build --app netmanthan_themes
2025-08-19 08:58:21,740 INFO /usr/local/bin/bench build --app netmanthan_themes
2025-08-19 08:58:34,164 INFO /usr/local/bin/bench --site your_site_name install-app netmanthan_themes
2025-08-19 08:58:49,639 INFO /usr/local/bin/bench --site erpnextapp install-app netmanthan_themes
2025-08-19 08:59:01,216 INFO /usr/local/bin/bench clear-cache
2025-08-19 08:59:08,087 INFO /usr/local/bin/bench build
2025-08-19 09:01:16,879 INFO /usr/local/bin/bench start
2025-08-19 09:01:54,137 INFO /usr/local/bin/bench --site site1.local uninstall-app my_theme
2025-08-19 09:02:30,261 INFO /usr/local/bin/bench --site erpnextapp uninstall-app netmanthan_themes
2025-08-19 09:11:00,695 INFO /usr/local/bin/bench new-app vuexy_theme
2025-08-19 09:11:00,703 LOG creating new app vuexy_theme
2025-08-19 09:17:01,716 LOG Installing vuexy_theme
2025-08-19 09:17:01,728 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/vuexy_theme 
2025-08-19 09:17:04,735 DEBUG bench build --app vuexy_theme
2025-08-19 09:17:04,889 INFO /usr/local/bin/bench build --app vuexy_theme
2025-08-19 09:17:05,692 WARNING bench build --app vuexy_theme executed with exit code 1
2025-08-19 09:17:05,692 WARNING /usr/local/bin/bench new-app vuexy_theme executed with exit code 1
2025-08-19 09:32:25,824 INFO /usr/local/bin/bench get-app healthcare https://github.com/frappe/healthcare
2025-08-19 09:32:25,865 LOG Getting healthcare
2025-08-19 09:32:25,865 DEBUG cd ./apps && git clone https://github.com/frappe/healthcare  --depth 1 --origin upstream
2025-08-19 09:32:26,970 LOG Installing healthcare
2025-08-19 09:32:26,972 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/healthcare 
2025-08-19 09:32:30,215 DEBUG bench build --app healthcare
2025-08-19 09:32:30,346 INFO /usr/local/bin/bench build --app healthcare
2025-08-19 09:32:54,008 INFO /usr/local/bin/bench --site site1.local install-app healthcare
2025-08-19 09:33:27,768 INFO /usr/local/bin/bench --site erpnextapp install-app healthcare
2025-08-19 09:34:36,339 INFO /usr/local/bin/bench get-app --branch version-15 healthcare https://github.com/frappe/healthcare
2025-08-19 09:34:36,352 LOG Getting healthcare
2025-08-19 09:34:36,353 DEBUG cd ./apps && git clone https://github.com/frappe/healthcare --branch version-15 --depth 1 --origin upstream
2025-08-19 09:34:37,037 LOG Installing healthcare
2025-08-19 09:34:37,037 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/healthcare 
2025-08-19 09:34:39,873 DEBUG bench build --app healthcare
2025-08-19 09:34:40,008 INFO /usr/local/bin/bench build --app healthcare
2025-08-19 09:35:00,393 INFO /usr/local/bin/bench --site erpnextapp install-app healthcare
2025-08-19 09:35:45,890 INFO /usr/local/bin/bench --site erpnextapp migrate
2025-08-19 09:36:08,957 INFO /usr/local/bin/bench build
2025-08-19 09:37:19,359 INFO /usr/local/bin/bench start
2025-08-19 09:39:33,220 INFO /usr/local/bin/bench restart
2025-08-19 09:39:37,191 INFO /usr/local/bin/bench restart
2025-08-19 09:42:50,105 INFO /usr/local/bin/bench version
2025-08-19 09:43:52,170 INFO /usr/local/bin/bench --site your-site-name uninstall-app hrms
2025-08-19 09:44:05,341 INFO /usr/local/bin/bench --site erpnextapp uninstall-app hrms
2025-08-19 09:44:45,689 INFO /usr/local/bin/bench --site your-site-name install-app healthcare
2025-08-19 09:44:59,187 INFO /usr/local/bin/bench --site erpnextapp install-app healthcare
2025-08-19 09:45:15,895 INFO /usr/local/bin/bench --site your-site-name migrate
2025-08-19 09:45:27,096 INFO /usr/local/bin/bench --site erpnextapp migrate
2025-08-19 09:45:47,791 INFO /usr/local/bin/bench build
2025-08-19 09:47:42,443 INFO /usr/local/bin/bench restart
2025-08-19 09:50:32,831 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-19 09:50:40,536 INFO /usr/local/bin/bench --site erpnextapp uninstall-app healthcare
2025-08-19 09:51:23,977 INFO /usr/local/bin/bench --site erpnextapp install-app healthcare
2025-08-19 09:52:36,178 INFO /usr/local/bin/bench --site erpnextapp migrate
2025-08-19 09:54:18,219 INFO /usr/local/bin/bench --site erpnextapp clear-cache
2025-08-19 09:54:24,770 INFO /usr/local/bin/bench --site erpnextapp clear-website-cache
2025-08-19 09:54:31,450 INFO /usr/local/bin/bench build
2025-08-19 09:55:33,060 INFO /usr/local/bin/bench restart
2025-08-19 09:56:30,953 INFO /usr/local/bin/bench --site erpnextapp uninstall-app healthcare
2025-08-19 10:07:09,507 INFO /usr/local/bin/bench install-app vuexy_theme
2025-08-19 10:08:58,835 INFO /usr/local/bin/bench --site erpnextapp list-app
2025-08-19 10:09:07,993 INFO /usr/local/bin/bench --site erpnextapp app-list
2025-08-19 10:09:12,320 INFO /usr/local/bin/bench --site erpnextapp app list
2025-08-19 10:10:57,373 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-19 10:11:52,085 INFO /usr/local/bin/bench --site erpnextapp install-app vuexy_theme
2025-08-19 10:12:00,141 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-19 10:12:11,321 INFO /usr/local/bin/bench clear-cache
2025-08-19 10:12:18,388 INFO /usr/local/bin/bench restart
2025-08-19 10:17:00,424 INFO /usr/local/bin/bench install-app vuexy_theme
2025-08-19 10:18:40,529 INFO /usr/local/bin/bench install-app vuexy_theme
2025-08-19 10:18:55,930 INFO /usr/local/bin/bench --site erpnextapp install-app vuexy_theme
2025-08-19 10:19:08,619 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-19 10:19:23,688 INFO /usr/local/bin/bench clear-cache
2025-08-19 10:19:37,255 INFO /usr/local/bin/bench build --app vuexy_theme
2025-08-19 10:20:08,376 INFO /usr/local/bin/bench restart
2025-08-19 10:23:16,932 INFO /usr/local/bin/bench status
2025-08-19 10:23:29,759 INFO /usr/local/bin/bench start
2025-08-19 10:26:52,523 INFO /usr/local/bin/bench build
2025-08-19 10:27:49,664 INFO /usr/local/bin/bench restart
2025-08-19 10:29:41,825 INFO /usr/local/bin/bench get-app vuexy_theme --resolve-deps
2025-08-19 10:29:52,884 WARNING /usr/local/bin/bench get-app vuexy_theme --resolve-deps executed with exit code 1
2025-08-19 10:30:17,072 INFO /usr/local/bin/bench remove-app vuexy_theme
2025-08-19 10:30:17,088 WARNING /usr/local/bin/bench remove-app vuexy_theme executed with exit code 1
2025-08-19 10:30:40,653 INFO /usr/local/bin/bench new-app vuexy_theme
2025-08-19 10:30:40,659 LOG creating new app vuexy_theme
2025-08-19 10:30:41,700 WARNING /usr/local/bin/bench new-app vuexy_theme executed with exit code 1
2025-08-19 10:31:41,182 INFO /usr/local/bin/bench new-app vuexy_theme
2025-08-19 10:31:41,187 LOG creating new app vuexy_theme
2025-08-19 10:34:54,079 LOG Installing vuexy_theme
2025-08-19 10:34:54,083 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/vuexy_theme 
2025-08-19 10:34:57,309 DEBUG bench build --app vuexy_theme
2025-08-19 10:34:57,424 INFO /usr/local/bin/bench build --app vuexy_theme
2025-08-19 10:34:58,156 WARNING bench build --app vuexy_theme executed with exit code 1
2025-08-19 10:34:58,157 WARNING /usr/local/bin/bench new-app vuexy_theme executed with exit code 1
2025-08-19 10:40:18,799 INFO /usr/local/bin/bench install-app vuexy_theme
2025-08-19 10:40:32,815 INFO /usr/local/bin/bench --site erpnextapp install-app vuexy_theme
2025-08-19 10:40:45,998 INFO /usr/local/bin/bench clear-cache
2025-08-19 10:40:56,071 INFO /usr/local/bin/bench restart
2025-08-19 10:42:57,586 INFO /usr/local/bin/bench restart
2025-08-19 10:43:42,031 INFO /usr/local/bin/bench restart
2025-08-19 10:44:14,456 INFO /usr/local/bin/bench --site erpnextapp mariadb
2025-08-19 10:45:02,330 INFO /usr/local/bin/bench --site erpnextapp uninstall-app vuexy_theme --yes
2025-08-19 10:46:45,628 INFO /usr/local/bin/bench --site erpnextapp console
2025-08-19 10:48:26,646 INFO /usr/local/bin/bench restart
2025-08-19 10:49:17,561 INFO /usr/local/bin/bench restart
2025-08-19 10:49:47,460 INFO /usr/local/bin/bench --site erpnextapp migrate
2025-08-19 10:52:43,083 INFO /usr/local/bin/bench clear-cache
2025-08-19 10:52:54,721 INFO /usr/local/bin/bench restart
2025-08-19 10:53:22,866 INFO /usr/local/bin/bench --site erpnextapp console
2025-08-19 10:54:22,744 INFO /usr/local/bin/bench --site erpnextapp migrate --skip-failing
2025-08-19 10:55:59,898 INFO /usr/local/bin/bench clear-cache
2025-08-19 10:56:01,251 INFO /usr/local/bin/bench restart
2025-08-19 11:13:37,824 INFO /usr/local/bin/bench build
2025-08-19 11:14:37,767 INFO /usr/local/bin/bench restart
2025-08-19 11:17:37,345 INFO /usr/local/bin/bench clear-cache
2025-08-19 11:17:49,302 INFO /usr/local/bin/bench restart
2025-08-19 11:18:43,728 INFO /usr/local/bin/bench --site erpnextapp console
2025-08-19 11:20:00,979 INFO /usr/local/bin/bench --site erpnextapp rebuild-global-search
2025-08-19 11:20:31,296 INFO /usr/local/bin/bench restart
2025-08-19 11:20:59,964 INFO /usr/local/bin/bench --site erpnextapp set-maintenance-mode on
2025-08-19 11:21:18,313 INFO /usr/local/bin/bench --site erpnextapp migrate --skip-failing
2025-08-19 11:23:17,531 INFO /usr/local/bin/bench setup requirements
2025-08-19 11:23:17,539 DEBUG /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-08-19 11:23:18,635 LOG Installing frappe
2025-08-19 11:23:18,637 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-08-19 11:23:21,520 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files
2025-08-19 11:23:21,522 WARNING cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files executed with exit code 126
2025-08-19 11:23:21,523 WARNING /usr/local/bin/bench setup requirements executed with exit code 1
2025-08-19 11:23:38,817 INFO /usr/local/bin/bench --site erpnextapp set-maintenance-mode off
2025-08-19 11:23:55,122 INFO /usr/local/bin/bench restart
2025-08-19 11:28:47,950 INFO /usr/local/bin/bench build
2025-08-19 11:29:45,487 INFO /usr/local/bin/bench restart
2025-08-19 11:35:26,213 INFO /usr/local/bin/bench build
2025-08-19 11:35:50,072 INFO /usr/local/bin/bench clear-cache
2025-08-19 12:00:02,028 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-19 12:12:19,734 INFO /usr/local/bin/bench restart
2025-08-19 12:12:30,838 INFO /usr/local/bin/bench start
2025-08-19 12:12:55,089 INFO /usr/local/bin/bench --site erpnextapp uninstall-app vuexy_theme
2025-08-19 12:13:06,297 INFO /usr/local/bin/bench --site erpnextapp app list
2025-08-19 18:00:02,249 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-20 00:00:03,325 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-20 06:00:03,019 INFO /usr/local/bin/bench --verbose --site all backup
2025-08-20 09:34:27,666 INFO /usr/local/bin/bench start
2025-08-20 09:37:39,423 INFO /usr/local/bin/bench start
2025-08-20 09:38:42,700 INFO /usr/local/bin/bench restart
2025-08-20 09:38:47,073 INFO /usr/local/bin/bench start
2025-08-20 09:42:38,955 INFO /usr/local/bin/bench start
2025-08-20 09:42:39,212 INFO /usr/local/bin/bench watch
2025-08-20 09:42:39,224 INFO /usr/local/bin/bench worker
2025-08-20 09:42:39,225 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 09:42:39,248 INFO /usr/local/bin/bench schedule
2025-08-20 09:43:05,457 INFO /usr/local/bin/bench start
2025-08-20 09:43:05,715 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 09:43:05,726 INFO /usr/local/bin/bench worker
2025-08-20 09:43:05,740 INFO /usr/local/bin/bench watch
2025-08-20 09:43:05,748 INFO /usr/local/bin/bench schedule
2025-08-20 09:43:47,394 INFO /usr/local/bin/bench start
2025-08-20 09:43:47,643 INFO /usr/local/bin/bench watch
2025-08-20 09:43:47,645 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 09:43:47,647 INFO /usr/local/bin/bench schedule
2025-08-20 09:43:47,678 INFO /usr/local/bin/bench worker
2025-08-20 09:46:08,412 INFO /usr/local/bin/bench --site erpnextapp app list
2025-08-20 09:46:22,346 INFO /usr/local/bin/bench --site erpnextapp app --help
2025-08-20 09:56:00,868 INFO /usr/local/bin/bench --site erpnextap uninstall-app vuexy_theme
2025-08-20 09:56:15,844 INFO /usr/local/bin/bench --site erpnextap list-apps
2025-08-20 09:56:48,512 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-20 09:57:00,176 INFO /usr/local/bin/bench build
2025-08-20 09:58:23,780 INFO /usr/local/bin/bench --site erpnextapp list-apps
2025-08-20 10:01:14,369 INFO /usr/local/bin/bench start
2025-08-20 10:01:14,614 INFO /usr/local/bin/bench watch
2025-08-20 10:01:14,618 INFO /usr/local/bin/bench schedule
2025-08-20 10:01:14,642 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 10:01:14,650 INFO /usr/local/bin/bench worker
2025-08-20 10:01:31,998 INFO /usr/local/bin/bench new-site erphealth
2025-08-20 10:03:31,108 INFO /usr/local/bin/bench --site erphealth install-app erpnext
2025-08-20 10:05:47,642 INFO /usr/local/bin/bench use erphealth
2025-08-20 10:06:24,779 INFO /usr/local/bin/bench build
2025-08-20 10:07:38,897 INFO /usr/local/bin/bench start
2025-08-20 10:07:39,118 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 10:07:39,129 INFO /usr/local/bin/bench schedule
2025-08-20 10:07:39,131 INFO /usr/local/bin/bench watch
2025-08-20 10:07:39,132 INFO /usr/local/bin/bench worker
2025-08-20 10:12:14,743 INFO /usr/local/bin/bench version
2025-08-20 10:13:27,689 INFO /usr/local/bin/bench --site erphealth install-app healthcare
2025-08-20 10:23:45,704 INFO /usr/local/bin/bench remove-app vuexy_theme
2025-08-20 10:23:45,712 WARNING /usr/local/bin/bench remove-app vuexy_theme executed with exit code 1
2025-08-20 10:25:07,737 INFO /usr/local/bin/bench new-app vuexy_theme
2025-08-20 10:25:07,743 LOG creating new app vuexy_theme
2025-08-20 10:25:58,216 LOG Installing vuexy_theme
2025-08-20 10:25:58,218 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/vuexy_theme 
2025-08-20 10:26:01,839 DEBUG bench build --app vuexy_theme
2025-08-20 10:26:01,991 INFO /usr/local/bin/bench build --app vuexy_theme
2025-08-20 10:28:49,186 INFO /usr/local/bin/bench build
2025-08-20 10:29:51,723 INFO /usr/local/bin/bench watch
2025-08-20 10:30:35,564 INFO /usr/local/bin/bench start
2025-08-20 10:30:35,843 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 10:30:35,843 INFO /usr/local/bin/bench watch
2025-08-20 10:30:35,870 INFO /usr/local/bin/bench schedule
2025-08-20 10:30:35,872 INFO /usr/local/bin/bench worker
2025-08-20 10:30:38,078 INFO /usr/local/bin/bench watch
2025-08-20 10:32:09,202 INFO /usr/local/bin/bench --site erphealth list-apps
2025-08-20 10:32:35,784 INFO /usr/local/bin/bench --site erphealth install-app vuexy_theme
2025-08-20 10:32:43,421 INFO /usr/local/bin/bench --site erphealth list-apps
2025-08-20 10:32:50,262 INFO /usr/local/bin/bench build
2025-08-20 10:33:56,785 INFO /usr/local/bin/bench start
2025-08-20 10:33:57,078 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 10:33:57,079 INFO /usr/local/bin/bench worker
2025-08-20 10:33:57,082 INFO /usr/local/bin/bench watch
2025-08-20 10:33:57,084 INFO /usr/local/bin/bench schedule
2025-08-20 10:34:00,435 INFO /usr/local/bin/bench watch
2025-08-20 10:44:00,845 INFO /usr/local/bin/bench build
2025-08-20 10:45:05,247 INFO /usr/local/bin/bench start
2025-08-20 10:45:05,496 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 10:45:05,508 INFO /usr/local/bin/bench worker
2025-08-20 10:45:05,526 INFO /usr/local/bin/bench schedule
2025-08-20 10:45:05,528 INFO /usr/local/bin/bench watch
2025-08-20 10:45:08,686 INFO /usr/local/bin/bench watch
2025-08-20 10:45:43,745 INFO /usr/local/bin/bench clear-cache
2025-08-20 10:46:43,457 INFO /usr/local/bin/bench clear-website-cache
2025-08-20 10:46:48,880 INFO /usr/local/bin/bench watch
2025-08-20 10:51:18,493 INFO /usr/local/bin/bench build
2025-08-20 10:52:33,356 INFO /usr/local/bin/bench watch
2025-08-20 11:11:58,438 INFO /usr/local/bin/bench build
2025-08-20 11:13:00,629 INFO /usr/local/bin/bench start
2025-08-20 11:13:00,869 INFO /usr/local/bin/bench serve --port 8000
2025-08-20 11:13:00,888 INFO /usr/local/bin/bench watch
2025-08-20 11:13:00,894 INFO /usr/local/bin/bench schedule
2025-08-20 11:13:00,902 INFO /usr/local/bin/bench worker
2025-08-20 11:13:03,438 INFO /usr/local/bin/bench watch
2025-08-20 12:00:02,549 INFO /usr/local/bin/bench --verbose --site all backup
