2025-08-19 09:35:07,406 WARNING database DDL Query made to DB:
create table `tabMedication Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`naming_series` varchar(140),
`medication` varchar(140),
`medication_item` varchar(140),
`order_date` date,
`expected_date` date,
`order_time` time(6),
`company` varchar(140),
`status` varchar(140) default 'draft-Medication Request Status',
`patient` varchar(140),
`patient_name` varchar(140),
`patient_gender` varchar(140),
`patient_birth_date` date,
`patient_age_data` varchar(140),
`patient_age` int(11) not null default 0,
`patient_blood_group` varchar(140),
`patient_email` varchar(140),
`patient_mobile` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`practitioner_email` varchar(140),
`medical_department` varchar(140),
`referred_to_practitioner` varchar(140),
`reason_reference_doctype` varchar(140),
`reason_reference` varchar(140),
`order_group` varchar(140),
`sequence` int(11) not null default 0,
`staff_role` varchar(140),
`item_code` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
`quantity` int(11) not null default 1,
`dosage_form` varchar(140),
`dosage` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`order_description` text,
`period` varchar(140),
`occurrence_time` time(6),
`total_dispensable_quantity` decimal(21,9) not null default 0,
`billing_status` varchar(140) default 'Pending',
`qty_invoiced` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `medication`(`medication`),
index `status`(`status`),
index `patient`(`patient`),
index `patient_email`(`patient_email`),
index `patient_mobile`(`patient_mobile`),
index `inpatient_record`(`inpatient_record`),
index `practitioner`(`practitioner`),
index `staff_role`(`staff_role`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:07,534 WARNING database DDL Query made to DB:
create table `tabABDM Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140),
`default` int(1) not null default 0,
`company` varchar(140),
`auth_base_url` varchar(140),
`client_id` varchar(140),
`client_secret` varchar(140),
`health_id_base_url` varchar(140),
`consent_base_url` varchar(140),
`patient_aadhaar_consent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:07,635 WARNING database DDL Query made to DB:
create table `tabMedication Linked Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item` varchar(140),
`item_group` varchar(140),
`stock_uom` varchar(140),
`brand` varchar(140),
`manufacturer` varchar(140),
`description` text,
`is_billable` int(1) not null default 1,
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:07,755 WARNING database DDL Query made to DB:
create table `tabAntibiotic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`antibiotic_name` varchar(140) unique,
`abbr` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:07,851 WARNING database DDL Query made to DB:
create table `tabMedical Department` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`department` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:07,978 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Unit Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`service_unit_type` varchar(140) unique,
`allow_appointments` int(1) not null default 0,
`overlap_appointments` int(1) not null default 0,
`inpatient_occupancy` int(1) not null default 0,
`is_billable` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`uom` varchar(140),
`no_of_hours` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`description` text,
`change_in_item` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:08,069 WARNING database DDL Query made to DB:
create table `tabTreatment Plan Template Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:08,177 WARNING database DDL Query made to DB:
create table `tabLab Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_code` varchar(140),
`observation_template` varchar(140),
`lab_test_name` varchar(140),
`invoiced` int(1) not null default 0,
`service_request` varchar(140),
`lab_test_comment` text,
`lab_test_created` int(1) not null default 0,
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
index `invoiced`(`invoiced`),
index `lab_test_created`(`lab_test_created`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:08,301 WARNING database DDL Query made to DB:
create table `tabNursing Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`date` date,
`user` varchar(140),
`company` varchar(140),
`service_unit` varchar(140),
`medical_department` varchar(140),
`status` varchar(140) default 'Draft',
`activity` varchar(140),
`mandatory` int(1) not null default 0,
`description` text,
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`requested_start_time` datetime(6),
`requested_end_time` datetime(6),
`duration` decimal(21,9),
`task_start_time` datetime(6),
`task_end_time` datetime(6),
`task_duration` decimal(21,9),
`reference_doctype` varchar(140),
`amended_from` varchar(140),
`reference_name` varchar(140),
`task_doctype` varchar(140),
`task_document_name` varchar(140),
`notes` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:08,420 WARNING database DDL Query made to DB:
create table `tabExercise Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise_name` varchar(140),
`difficulty_level` varchar(140),
`description` longtext,
`exercise_steps` text,
`exercise_video` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:08,542 WARNING database DDL Query made to DB:
create table `tabCode Value` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system_uri` varchar(140),
`experimental` int(1) not null default 0,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`code_value` varchar(140),
`value_set` varchar(140),
`display` text,
`status` varchar(140),
`version` varchar(140),
`level` int(10) not null default 0,
`definition` text,
`official_url` varchar(140),
`canonical_mapping` varchar(140),
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_system`(`code_system`),
index `system_uri`(`system_uri`),
index `code_value`(`code_value`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:08,680 WARNING database DDL Query made to DB:
create table `tabComplaint` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`complaints` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:08,882 WARNING database DDL Query made to DB:
create table `tabLab Test` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`template` varchar(140),
`lab_test_name` varchar(140),
`lab_test_group` varchar(140),
`department` varchar(140),
`company` varchar(140),
`status` varchar(140),
`date` date,
`time` time(6),
`submitted_date` datetime(6),
`result_date` date,
`approved_date` datetime(6),
`expected_result_date` date,
`expected_result_time` time(6),
`printed_on` datetime(6),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_sex` varchar(140),
`inpatient_record` varchar(140),
`report_preference` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`service_unit` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`requesting_department` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`employee_designation` varchar(140),
`user` varchar(140),
`sample` varchar(140),
`descriptive_result` longtext,
`lab_test_comment` text,
`custom_result` longtext,
`worksheet_instructions` longtext,
`legend_print_position` varchar(140),
`result_legend` longtext,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`email_sent` int(1) not null default 0,
`sms_sent` int(1) not null default 0,
`printed` int(1) not null default 0,
`normal_toggle` int(1) not null default 0,
`imaging_toggle` int(1) not null default 0,
`descriptive_toggle` int(1) not null default 0,
`sensitivity_toggle` int(1) not null default 0,
`amended_from` varchar(140),
`prescription` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `lab_test_name`(`lab_test_name`),
index `department`(`department`),
index `status`(`status`),
index `result_date`(`result_date`),
index `patient`(`patient`),
index `mobile`(`mobile`),
index `practitioner`(`practitioner`),
index `service_request`(`service_request`),
index `invoiced`(`invoiced`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,032 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`plan_name` varchar(140) unique,
`link_existing_item` int(1) not null default 0,
`linked_item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`description` text,
`total_sessions` int(11) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,140 WARNING database DDL Query made to DB:
create table `tabObservation Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`abbr` varchar(140),
`condition` longtext,
`based_on_formula` int(1) not null default 0,
`formula` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,231 WARNING database DDL Query made to DB:
create table `tabSample Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sample_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,346 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`title` varchar(140) unique,
`department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,463 WARNING database DDL Query made to DB:
create table `tabMedication Ingredient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,569 WARNING database DDL Query made to DB:
create table `tabFee Validity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
`patient` varchar(140),
`medical_department` varchar(140),
`status` varchar(140),
`patient_appointment` varchar(140),
`sales_invoice_ref` varchar(140),
`max_visits` int(11) not null default 0,
`visited` int(11) not null default 0,
`start_date` date,
`valid_till` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `practitioner`(`practitioner`),
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,688 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_parameter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,796 WARNING database DDL Query made to DB:
create table `tabMedication Class` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:09,906 WARNING database DDL Query made to DB:
create table `tabClinical Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`clinical_note_type` varchar(140),
`terms_and_conditions` varchar(140),
`posting_date` datetime(6),
`practitioner` varchar(140),
`user` varchar(140),
`note` longtext,
`reference_doc` varchar(140),
`reference_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:10,019 WARNING database DDL Query made to DB:
create table `tabPatient Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`therapy_session` varchar(140),
`patient` varchar(140),
`assessment_template` varchar(140),
`company` varchar(140),
`healthcare_practitioner` varchar(140),
`assessment_datetime` datetime(6),
`assessment_description` text,
`total_score_obtained` int(11) not null default 0,
`total_score` int(11) not null default 0,
`scale_min` int(11) not null default 0,
`scale_max` int(11) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:10,289 WARNING database DDL Query made to DB:
create table `tabDescriptive Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_particulars` varchar(140),
`result_value` text,
`allow_blank` int(1) not null default 1,
`template` varchar(140),
`require_result_value` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:10,390 WARNING database DDL Query made to DB:
create table `tabExercise Difficulty Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`difficulty_level` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:10,528 WARNING database DDL Query made to DB:
create table `tabTherapy Session` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`appointment` varchar(140),
`therapy_plan` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`gender` varchar(140),
`company` varchar(140),
`therapy_type` varchar(140),
`practitioner` varchar(140),
`department` varchar(140),
`duration` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`location` varchar(140),
`service_unit` varchar(140),
`start_date` date,
`start_time` time(6),
`total_counts_targeted` int(11) not null default 0,
`total_counts_completed` int(11) not null default 0,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:10,646 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient_encounter` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`inpatient_record` varchar(140),
`company` varchar(140),
`status` varchar(140),
`practitioner` varchar(140),
`start_date` date,
`end_date` date,
`total_orders` decimal(21,9) not null default 0,
`completed_orders` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:10,757 WARNING database DDL Query made to DB:
create table `tabDrug Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`drug_code` varchar(140),
`drug_name` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`dosage_form` varchar(140),
`dosage_by_interval` int(1) not null default 0,
`dosage` varchar(140),
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`period` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`intent` varchar(140),
`priority` varchar(140),
`medication_request` varchar(140),
`comment` text,
`update_schedule` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:10,844 WARNING database DDL Query made to DB:
create table `tabLab Test UOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_uom` varchar(140) unique,
`uom_description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:10,948 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140),
`mandatory` int(1) not null default 0,
`type` varchar(140),
`time_offset` decimal(21,9),
`description` text,
`task_duration` decimal(21,9),
`task_doctype` varchar(140),
`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,030 WARNING database DDL Query made to DB:
create table `tabPatient Encounter Symptom` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`complaint` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,143 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Entry Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`service_unit` varchar(140),
`datetime` datetime(6),
`drug_code` varchar(140),
`drug_name` varchar(140),
`dosage` decimal(21,9) not null default 0,
`available_qty` decimal(21,9) not null default 0,
`dosage_form` varchar(140),
`instructions` text,
`against_imo` varchar(140),
`against_imoe` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,226 WARNING database DDL Query made to DB:
create table `tabFee Validity Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,316 WARNING database DDL Query made to DB:
create table `tabTreatment Plan Template Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`template` varchar(140),
`qty` int(11) not null default 1,
`instructions` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,400 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`no_of_sessions` int(11) not null default 0,
`sessions_completed` int(11) not null default 0,
`service_request` varchar(140),
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,588 WARNING database DDL Query made to DB:
create table `tabObservation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`observation_template` varchar(140),
`observation_category` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140) default 'Registered',
`medical_department` varchar(140),
`amended_from` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`healthcare_practitioner` varchar(140),
`practitioner_name` varchar(140),
`has_component` int(1) not null default 0,
`preferred_display_name` varchar(140),
`sample_collection_required` int(1) not null default 0,
`permitted_unit` varchar(140),
`sample` varchar(140),
`sample_type` varchar(140),
`permitted_data_type` varchar(140),
`method` varchar(140),
`specimen` varchar(140),
`sample_collection_time` datetime(6),
`sample_status` varchar(140),
`result_template` varchar(140),
`result_attach` text,
`result_boolean` varchar(140),
`result_data` varchar(140),
`result_text` longtext,
`result_float` decimal(21,9) not null default 0,
`result_select` varchar(140),
`result_datetime` datetime(6),
`result_time` datetime(6),
`result_period_from` datetime(6),
`result_period_to` datetime(6),
`options` text,
`time_of_result` datetime(6),
`time_of_approval` datetime(6),
`interpretation_template` varchar(140),
`result_interpretation` longtext,
`observation_method` varchar(140),
`reference` text,
`note` longtext,
`description` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`sales_invoice` varchar(140),
`sales_invoice_status` varchar(140),
`sales_invoice_item` varchar(140),
`service_request` varchar(140),
`disapproval_reason` text,
`parent_observation` varchar(140),
`observation_idx` int(11) not null default 0,
`days` int(11) not null default 0,
`invoiced` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,766 WARNING database DDL Query made to DB:
create table `tabBody Part Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`body_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,850 WARNING database DDL Query made to DB:
create table `tabPractitioner Service Unit Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`schedule` varchar(140),
`service_unit` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:11,934 WARNING database DDL Query made to DB:
create table `tabNormal Test Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_event` varchar(140),
`allow_blank` int(1) not null default 0,
`lab_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`normal_range` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,012 WARNING database DDL Query made to DB:
create table `tabPatient Encounter Diagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,119 WARNING database DDL Query made to DB:
create table `tabDiagnostic Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`age` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`naming_series` varchar(140),
`ref_doctype` varchar(140),
`docname` varchar(140),
`reference_posting_date` date,
`sample_collection` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `sample_collection`(`sample_collection`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,210 WARNING database DDL Query made to DB:
create table `tabOrganism Test Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140),
`colony_population` text,
`colony_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,303 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_parameter` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,401 WARNING database DDL Query made to DB:
create table `tabPractitioner Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`allow_video_conferencing` int(1) not null default 0,
`schedule_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,553 WARNING database DDL Query made to DB:
create table `tabInpatient Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`dob` date,
`mobile` varchar(140),
`email` varchar(140),
`phone` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Admission Scheduled',
`scheduled_date` date,
`admitted_datetime` datetime(6),
`expected_discharge` date,
`admission_encounter` varchar(140),
`admission_practitioner` varchar(140),
`medical_department` varchar(140),
`admission_ordered_for` date,
`admission_service_unit_type` varchar(140),
`admission_nursing_checklist_template` varchar(140),
`discharge_nursing_checklist_template` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`admission_instruction` text,
`therapy_plan` varchar(140),
`discharge_ordered_date` date,
`discharge_practitioner` varchar(140),
`discharge_encounter` varchar(140),
`discharge_datetime` datetime(6),
`discharge_instructions` text,
`followup_date` date,
`discharge_note` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,706 WARNING database DDL Query made to DB:
create table `tabTherapy Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`therapy_type` varchar(140) unique,
`medical_department` varchar(140),
`description` text,
`default_duration` int(11) not null default 0,
`healthcare_service_unit` varchar(140),
`nursing_checklist_template` varchar(140),
`item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,836 WARNING database DDL Query made to DB:
create table `tabCode System` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`code_system` varchar(140) unique,
`uri` varchar(140),
`description` text,
`status` varchar(140),
`version` varchar(140),
`is_fhir_defined` int(1) not null default 1,
`oid` varchar(140) unique,
`experimental` int(1) not null default 1,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `disabled`(`disabled`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:12,958 WARNING database DDL Query made to DB:
create table `tabOrganism Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140),
`colony_population` text,
`colony_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:13,093 WARNING database DDL Query made to DB:
create sequence if not exists observation_sample_collection_id_seq nocache nocycle
2025-08-19 09:35:13,100 WARNING database DDL Query made to DB:
create table `tabObservation Sample Collection` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`has_component` int(1) not null default 0,
`sample` varchar(140) default 'Urine',
`sample_type` varchar(140),
`uom` varchar(140),
`status` varchar(140),
`container_closure_color` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`medical_department` varchar(140),
`collection_date_time` datetime(6),
`collection_point` varchar(140),
`collected_user` varchar(140),
`collected_by` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`reference_child` varchar(140),
`service_request` varchar(140),
`specimen` varchar(140),
`component_observation_parent` varchar(140),
`component_observations` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:13,237 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140) unique,
`medical_department` varchar(140),
`description` text,
`disabled` int(1) not null default 0,
`pre_op_nursing_checklist_template` varchar(140),
`post_op_nursing_checklist_template` varchar(140),
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`consume_stock` int(1) not null default 0,
`sample_uom` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`sample` varchar(140),
`sample_details` text,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `consume_stock`(`consume_stock`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:13,388 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Unit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_service_unit_name` varchar(140),
`is_group` int(1) not null default 0,
`service_unit_type` varchar(140),
`allow_appointments` int(1) not null default 0,
`overlap_appointments` int(1) not null default 0,
`service_unit_capacity` int(11) not null default 0,
`inpatient_occupancy` int(1) not null default 0,
`occupancy_status` varchar(140),
`company` varchar(140),
`warehouse` varchar(140),
`parent_healthcare_service_unit` varchar(140),
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `inpatient_occupancy`(`inpatient_occupancy`),
index `company`(`company`),
index `lft`(`lft`),
index `rgt`(`rgt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:13,475 WARNING database DDL Query made to DB:
alter table `tabHealthcare Service Unit`
					add unique `unique_service_unit_company`(healthcare_service_unit_name, company)
2025-08-19 09:35:13,527 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Order Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug` varchar(140),
`drug_name` varchar(140),
`dosage` decimal(21,9) not null default 0,
`dosage_form` varchar(140),
`instructions` text,
`date` date,
`time` time(6),
`is_completed` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:13,615 WARNING database DDL Query made to DB:
create table `tabLab Test Group Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_or_new_line` varchar(140) default 'Add Test',
`lab_test_template` varchar(140),
`lab_test_rate` decimal(21,9) not null default 0,
`lab_test_description` varchar(140),
`group_event` varchar(140),
`group_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`allow_blank` int(1) not null default 0,
`group_test_normal_range` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:13,751 WARNING database DDL Query made to DB:
create table `tabObservation Reference Range` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`permitted_data_type` varchar(140),
`reference_type` varchar(140),
`applies_to` varchar(140),
`age` varchar(140),
`gestational_age` decimal(21,9) not null default 0,
`from_age_type` varchar(140) default 'Years',
`age_from` varchar(140),
`to_age_type` varchar(140) default 'Years',
`age_to` varchar(140),
`reference_from` varchar(140),
`datetime` datetime(6),
`from_duration` decimal(21,9),
`boolean_value` varchar(140),
`ratio` varchar(140),
`options` varchar(140),
`from_datetime` datetime(6),
`conditions` longtext,
`reference_to` varchar(140),
`to_datetime` datetime(6),
`to_duration` decimal(21,9),
`short_interpretation` varchar(140),
`long_interpretation` text,
`normal_boolean_value` varchar(140),
`normal_ratio` varchar(140),
`normal_from` decimal(21,9) not null default 0,
`normal_select` varchar(140),
`normal_condition` longtext,
`normal_interpretation` varchar(140),
`normal_to` decimal(21,9) not null default 0,
`normal_long_interpretation` text,
`abnormal_boolean_value` varchar(140),
`abnormal_from` decimal(21,9) not null default 0,
`abnormal_ratio` varchar(140),
`abnormal_select` varchar(140),
`abnormal_condition` longtext,
`abnormal_interpretation` varchar(140),
`abnormal_to` decimal(21,9) not null default 0,
`abnormal_long_interpretation` text,
`critical_boolean_value` varchar(140),
`critical_from` decimal(21,9) not null default 0,
`critical_ratio` varchar(140),
`critical_select` varchar(140),
`critical_condition` longtext,
`critical_interpretation` varchar(140),
`critical_to` decimal(21,9) not null default 0,
`critical_long_interpretation` text,
`reference_text` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:13,837 WARNING database DDL Query made to DB:
create table `tabService Request Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_care_type`(`patient_care_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:13,955 WARNING database DDL Query made to DB:
create table `tabAppointment Type Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140),
`dn` varchar(140),
`op_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:14,044 WARNING database DDL Query made to DB:
create table `tabHealthcare Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140) unique,
`description` text,
`activity_duration` decimal(21,9),
`role` varchar(140),
`task_doctype` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:14,137 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`barcode` varchar(140),
`uom` varchar(140),
`invoice_separately_as_consumables` int(1) not null default 0,
`batch_no` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
index `item_code`(`item_code`),
index `actual_qty`(`actual_qty`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:14,231 WARNING database DDL Query made to DB:
create table `tabExercise` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise_type` varchar(140),
`difficulty_level` varchar(140),
`counts_target` int(11) not null default 0,
`counts_completed` int(11) not null default 0,
`assistance_level` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:14,336 WARNING database DDL Query made to DB:
create table `tabSensitivity Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`antibiotic` varchar(140),
`antibiotic_sensitivity` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:14,433 WARNING database DDL Query made to DB:
create table `tabOrganism` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140) unique,
`abbr` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:14,541 WARNING database DDL Query made to DB:
create table `tabPrescription Duration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number` int(11) not null default 0,
`period` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:14,715 WARNING database DDL Query made to DB:
create table `tabPatient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`patient_name` varchar(140),
`sex` varchar(140),
`blood_group` varchar(140),
`dob` date,
`image` text,
`status` varchar(140),
`uid` varchar(140) unique,
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`report_preference` varchar(140),
`mobile` varchar(140),
`phone` varchar(140),
`email` varchar(140),
`invite_user` int(1) not null default 1,
`user_id` varchar(140),
`customer` varchar(140),
`customer_group` varchar(140),
`territory` varchar(140),
`default_currency` varchar(140),
`default_price_list` varchar(140),
`language` varchar(140),
`patient_details` text,
`occupation` varchar(140),
`marital_status` varchar(140),
`allergies` text,
`medication` text,
`medical_history` text,
`surgical_history` text,
`tobacco_past_use` varchar(140),
`tobacco_current_use` varchar(140),
`alcohol_past_use` varchar(140),
`alcohol_current_use` varchar(140),
`surrounding_factors` text,
`other_risk_factors` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_name`(`patient_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:14,922 WARNING database DDL Query made to DB:
create table `tabAppointment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment_type` varchar(140) unique,
`default_duration` int(11) not null default 0,
`allow_booking_for` varchar(140) default 'Practitioner',
`color` varchar(140),
`price_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:15,077 WARNING database DDL Query made to DB:
create table `tabVital Signs` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`appointment` varchar(140),
`encounter` varchar(140),
`company` varchar(140),
`signs_date` date,
`signs_time` time(6),
`temperature` varchar(140),
`pulse` varchar(140),
`respiratory_rate` varchar(140),
`tongue` varchar(140),
`abdomen` varchar(140),
`reflexes` varchar(140),
`bp_systolic` varchar(140),
`bp_diastolic` varchar(140),
`bp` varchar(140),
`vital_signs_note` text,
`height` decimal(21,9) not null default 0,
`weight` decimal(21,9) not null default 0,
`bmi` decimal(21,9) not null default 0,
`nutrition_note` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:15,236 WARNING database DDL Query made to DB:
create table `tabObservation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation` varchar(140) unique,
`observation_category` varchar(140),
`preferred_display_name` varchar(140),
`abbr` varchar(140),
`has_component` int(1) not null default 0,
`medical_department` varchar(140),
`description` longtext,
`method` varchar(140),
`method_value` varchar(140),
`service_unit` varchar(140),
`result_template` varchar(140),
`interpretation_template` varchar(140),
`permitted_data_type` varchar(140),
`permitted_unit` varchar(140),
`options` text,
`template` varchar(140),
`sample_collection_required` int(1) not null default 0,
`sample` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`uom` varchar(140),
`sample_type` varchar(140),
`container_closure_color` varchar(140),
`sample_details` text,
`is_billable` int(1) not null default 0,
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_billable`(`is_billable`),
index `item`(`item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:15,334 WARNING database DDL Query made to DB:
create table `tabPatient Relation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`relation` varchar(140),
`description` text,
index `relation`(`relation`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:15,421 WARNING database DDL Query made to DB:
create table `tabExercise Type Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`image` text,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:35:20,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `patient` varchar(140), ADD COLUMN `patient_name` varchar(140), ADD COLUMN `ref_practitioner` varchar(140), ADD COLUMN `service_unit` varchar(140)
2025-08-19 09:35:20,629 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-08-19 09:35:20,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `inpatient_medication_entry` varchar(140)
2025-08-19 09:35:20,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-08-19 09:35:20,776 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `patient` varchar(140), ADD COLUMN `inpatient_medication_entry_child` varchar(140)
2025-08-19 09:35:20,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0
2025-08-19 09:35:20,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `reference_dt` varchar(140), ADD COLUMN `reference_dn` varchar(140), ADD COLUMN `practitioner` varchar(140), ADD COLUMN `medical_department` varchar(140), ADD COLUMN `service_unit` varchar(140)
2025-08-19 09:35:20,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-08-19 09:35:20,876 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `creation`(`creation`)
2025-08-19 09:35:46,600 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-19 09:35:47,538 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-19 09:35:48,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-19 09:44:13,243 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabJob Offer`
2025-08-19 09:44:13,276 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Tax Exemption Declaration Category`
2025-08-19 09:44:13,308 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabIdentification Document Type`
2025-08-19 09:44:13,351 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTraining Program`
2025-08-19 09:44:13,409 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Performance Feedback`
2025-08-19 09:44:13,454 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDaily Work Summary`
2025-08-19 09:44:13,506 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Advance`
2025-08-19 09:44:13,556 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Checkin`
2025-08-19 09:44:13,614 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Structure`
2025-08-19 09:44:13,683 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExpense Claim`
2025-08-19 09:44:13,719 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Tax Exemption Proof Submission Detail`
2025-08-19 09:44:13,755 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabJob Applicant Source`
2025-08-19 09:44:13,789 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppointment Letter content`
2025-08-19 09:44:13,831 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Promotion`
2025-08-19 09:44:13,866 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExpense Claim Account`
2025-08-19 09:44:13,912 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Detail`
2025-08-19 09:44:13,942 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabOvertime Salary Component`
2025-08-19 09:44:13,976 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Benefit Application Detail`
2025-08-19 09:44:14,031 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabJob Opening`
2025-08-19 09:44:14,073 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabGrievance Type`
2025-08-19 09:44:14,131 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTravel Request`
2025-08-19 09:44:14,176 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInterview Feedback`
2025-08-19 09:44:14,213 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPurpose of Travel`
2025-08-19 09:44:14,249 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Slip Leave`
2025-08-19 09:44:14,297 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Type`
2025-08-19 09:44:14,334 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPWA Notification`
2025-08-19 09:44:14,381 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Tax Exemption Proof Submission`
2025-08-19 09:44:14,432 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Grievance`
2025-08-19 09:44:14,473 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInterest`
2025-08-19 09:44:14,509 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Tax Exemption Sub Category`
2025-08-19 09:44:14,546 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Skill Map`
2025-08-19 09:44:14,583 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Boarding Activity`
2025-08-19 09:44:14,629 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabJob Requisition`
2025-08-19 09:44:14,668 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppointment Letter Template`
2025-08-19 09:44:14,705 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPayroll Period Date`
2025-08-19 09:44:14,746 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInterview Round`
2025-08-19 09:44:14,788 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Policy Assignment`
2025-08-19 09:44:14,827 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Withholding Cycle`
2025-08-19 09:44:14,866 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTraining Result Employee`
2025-08-19 09:44:14,899 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExpense Claim Advance`
2025-08-19 09:44:14,930 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Policy Detail`
2025-08-19 09:44:14,970 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAttendance Request`
2025-08-19 09:44:15,007 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppraisal Template Goal`
2025-08-19 09:44:15,047 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInterview Detail`
2025-08-19 09:44:15,086 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabStaffing Plan Detail`
2025-08-19 09:44:15,139 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExit Interview`
2025-08-19 09:44:15,177 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppraisal Goal`
2025-08-19 09:44:15,235 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShift Assignment`
2025-08-19 09:44:15,271 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSkill Assessment`
2025-08-19 09:44:15,309 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabJob Offer Term Template`
2025-08-19 09:44:15,351 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Onboarding Template`
2025-08-19 09:44:15,403 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabOvertime Type`
2025-08-19 09:44:15,436 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExpense Claim Type`
2025-08-19 09:44:15,476 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabVehicle Log`
2025-08-19 09:44:15,513 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Onboarding`
2025-08-19 09:44:15,561 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabGoal`
2025-08-19 09:44:15,597 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDepartment Approver`
2025-08-19 09:44:15,641 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppointment Letter`
2025-08-19 09:44:15,672 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Block List Date`
2025-08-19 09:44:15,706 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPayroll Period`
2025-08-19 09:44:15,747 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Ledger Entry`
2025-08-19 09:44:15,773 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDesignation Skill`
2025-08-19 09:44:15,816 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Withholding`
2025-08-19 09:44:15,858 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Benefit Application`
2025-08-19 09:44:15,902 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabJob Applicant`
2025-08-19 09:44:15,934 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployment Type`
2025-08-19 09:44:15,978 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabCompensatory Leave Request`
2025-08-19 09:44:16,018 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPayroll Employee Detail`
2025-08-19 09:44:16,062 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTraining Feedback`
2025-08-19 09:44:16,095 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabOffer Term`
2025-08-19 09:44:16,129 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInterviewer`
2025-08-19 09:44:16,176 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShift Request`
2025-08-19 09:44:16,216 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTraining Event Employee`
2025-08-19 09:44:16,261 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabFull and Final Asset`
2025-08-19 09:44:16,305 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Other Income`
2025-08-19 09:44:16,357 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Structure Assignment`
2025-08-19 09:44:16,399 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Block List`
2025-08-19 09:44:16,443 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Tax Exemption Declaration`
2025-08-19 09:44:16,490 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Incentive`
2025-08-19 09:44:16,529 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabStaffing Plan`
2025-08-19 09:44:16,570 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Separation`
2025-08-19 09:44:16,609 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTaxable Salary Slab`
2025-08-19 09:44:16,672 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Component`
2025-08-19 09:44:16,736 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAttendance`
2025-08-19 09:44:16,782 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExpense Claim Detail`
2025-08-19 09:44:16,848 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShift Type`
2025-08-19 09:44:16,885 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Feedback Criteria`
2025-08-19 09:44:16,928 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabRetention Bonus`
2025-08-19 09:44:17,017 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Component Account`
2025-08-19 09:44:17,089 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabFull and Final Statement`
2025-08-19 09:44:17,132 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTraining Result`
2025-08-19 09:44:17,173 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Property History`
2025-08-19 09:44:17,229 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppraisal`
2025-08-19 09:44:17,291 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Referral`
2025-08-19 09:44:17,326 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabIncome Tax Slab Other Charges`
2025-08-19 09:44:17,359 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Block List Allow`
2025-08-19 09:44:17,400 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabIncome Tax Slab`
2025-08-19 09:44:17,431 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Health Insurance`
2025-08-19 09:44:17,477 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Transfer`
2025-08-19 09:44:17,512 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExpense Taxes and Charges`
2025-08-19 09:44:17,546 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShift Location`
2025-08-19 09:44:17,590 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Allocation`
2025-08-19 09:44:17,629 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabGratuity Rule`
2025-08-19 09:44:17,675 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTraining Event`
2025-08-19 09:44:17,708 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDaily Work Summary Group User`
2025-08-19 09:44:17,744 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShift Schedule`
2025-08-19 09:44:17,778 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Feedback Rating`
2025-08-19 09:44:17,846 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Slip`
2025-08-19 09:44:17,888 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInterview Type`
2025-08-19 09:44:17,921 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabFull and Final Outstanding Statement`
2025-08-19 09:44:17,976 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPayroll Entry`
2025-08-19 09:44:18,011 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Skill`
2025-08-19 09:44:18,042 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabVehicle Service`
2025-08-19 09:44:18,091 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabGratuity`
2025-08-19 09:44:18,135 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAdditional Salary`
2025-08-19 09:44:18,183 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Encashment`
2025-08-19 09:44:18,218 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSkill`
2025-08-19 09:44:18,253 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Policy`
2025-08-19 09:44:18,282 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppraisal KRA`
2025-08-19 09:44:18,315 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabShift Schedule Assignment`
2025-08-19 09:44:18,348 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabOvertime Details`
2025-08-19 09:44:18,379 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Tax Exemption Category`
2025-08-19 09:44:18,420 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppraisal Cycle`
2025-08-19 09:44:18,453 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExpected Skill Set`
2025-08-19 09:44:18,503 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Separation Template`
2025-08-19 09:44:18,540 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Slip Loan`
2025-08-19 09:44:18,584 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppraisal Template`
2025-08-19 09:44:18,629 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInterview`
2025-08-19 09:44:18,671 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Application`
2025-08-19 09:44:18,703 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Training`
2025-08-19 09:44:18,744 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDaily Work Summary Group`
2025-08-19 09:44:18,780 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabGratuity Rule Slab`
2025-08-19 09:44:18,822 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTravel Itinerary`
2025-08-19 09:44:18,853 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSalary Slip Timesheet`
2025-08-19 09:44:18,882 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Cost Center`
2025-08-19 09:44:18,912 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTravel Request Costing`
2025-08-19 09:44:18,943 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabVehicle Service Item`
2025-08-19 09:44:18,982 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLeave Period`
2025-08-19 09:44:19,022 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabGratuity Applicable Component`
2025-08-19 09:44:19,072 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabOvertime Slip`
2025-08-19 09:44:19,107 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Benefit Claim`
2025-08-19 09:44:19,137 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabEmployee Grade`
2025-08-19 09:44:19,170 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabJob Offer Term`
2025-08-19 09:44:19,202 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppraisee`
2025-08-19 09:44:19,241 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabKRA`
2025-08-19 09:45:27,849 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-19 09:45:28,709 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-19 09:45:29,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-19 09:50:52,102 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTherapy Session`
2025-08-19 09:50:52,138 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabMedical Department`
2025-08-19 09:50:52,175 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Medical Record`
2025-08-19 09:50:52,222 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTherapy Plan`
2025-08-19 09:50:52,265 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient History Standard Document Type`
2025-08-19 09:50:52,310 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDiagnostic Report`
2025-08-19 09:50:52,349 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAntibiotic`
2025-08-19 09:50:52,389 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabFee Validity Reference`
2025-08-19 09:50:52,458 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabObservation Reference Range`
2025-08-19 09:50:52,489 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Relation`
2025-08-19 09:50:52,552 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLab Test`
2025-08-19 09:50:52,587 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLab Test Sample`
2025-08-19 09:50:52,617 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabBody Part Link`
2025-08-19 09:50:52,649 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDosage Form`
2025-08-19 09:50:52,708 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabService Request`
2025-08-19 09:50:52,738 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabCode Value Set`
2025-08-19 09:50:52,769 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Encounter Symptom`
2025-08-19 09:50:52,800 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClinical Note Type`
2025-08-19 09:50:52,842 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabMedication`
2025-08-19 09:50:52,879 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHealthcare Activity`
2025-08-19 09:50:52,912 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabService Request Reason`
2025-08-19 09:50:52,944 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClinical Procedure Item`
2025-08-19 09:50:52,971 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExercise Type Step`
2025-08-19 09:50:53,005 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabNormal Test Result`
2025-08-19 09:50:53,034 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTreatment Plan Template Practitioner`
2025-08-19 09:50:53,084 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTherapy Type`
2025-08-19 09:50:53,127 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInpatient Medication Entry`
2025-08-19 09:50:53,160 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPrescription Duration`
2025-08-19 09:50:53,202 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabObservation Sample Collection`
2025-08-19 09:50:53,267 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLab Test Template`
2025-08-19 09:50:53,303 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Assessment Sheet`
2025-08-19 09:50:53,337 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTreatment Plan Template Item`
2025-08-19 09:50:53,396 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClinical Procedure Template`
2025-08-19 09:50:53,429 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLab Test UOM`
2025-08-19 09:50:53,468 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabFee Validity`
2025-08-19 09:50:53,541 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabObservation`
2025-08-19 09:50:53,577 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Care Type`
2025-08-19 09:50:53,635 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInpatient Record`
2025-08-19 09:50:53,666 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPrescription Dosage`
2025-08-19 09:50:53,697 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Assessment Detail`
2025-08-19 09:50:53,729 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDescriptive Test Template`
2025-08-19 09:50:53,756 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabOrganism Test Item`
2025-08-19 09:50:53,788 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabMedication Linked Item`
2025-08-19 09:50:53,824 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClinical Note`
2025-08-19 09:50:53,857 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabABDM Request`
2025-08-19 09:50:53,889 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSensitivity Test Result`
2025-08-19 09:50:53,952 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabMedication Request`
2025-08-19 09:50:53,995 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSample Collection`
2025-08-19 09:50:54,022 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabOrganism`
2025-08-19 09:50:54,068 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHealthcare Practitioner`
2025-08-19 09:50:54,102 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Assessment Template`
2025-08-19 09:50:54,132 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHealthcare Schedule Time Slot`
2025-08-19 09:50:54,180 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Appointment`
2025-08-19 09:50:54,213 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabProcedure Prescription`
2025-08-19 09:50:54,252 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppointment Type Service Item`
2025-08-19 09:50:54,294 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabVital Signs`
2025-08-19 09:50:54,334 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabNursing Checklist Template Task`
2025-08-19 09:50:54,376 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Assessment`
2025-08-19 09:50:54,419 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHealthcare Service Unit Type`
2025-08-19 09:50:54,475 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTreatment Plan Template`
2025-08-19 09:50:54,537 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabClinical Procedure`
2025-08-19 09:50:54,570 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Encounter Diagnosis`
2025-08-19 09:50:54,602 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSensitivity`
2025-08-19 09:50:54,671 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabObservation Template`
2025-08-19 09:50:54,729 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabCode System`
2025-08-19 09:50:54,801 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient`
2025-08-19 09:50:54,848 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabCode Value`
2025-08-19 09:50:54,885 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabComplaint`
2025-08-19 09:50:54,947 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Encounter`
2025-08-19 09:50:54,978 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLab Test Group Template`
2025-08-19 09:50:55,010 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabNormal Test Template`
2025-08-19 09:50:55,040 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTherapy Plan Template Detail`
2025-08-19 09:50:55,079 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHealthcare Service Unit`
2025-08-19 09:50:55,108 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPractitioner Service Unit Schedule`
2025-08-19 09:50:55,141 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExercise`
2025-08-19 09:50:55,177 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPractitioner Schedule`
2025-08-19 09:50:55,214 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabObservation Component`
2025-08-19 09:50:55,250 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabAppointment Type`
2025-08-19 09:50:55,294 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTherapy Plan Detail`
2025-08-19 09:50:55,330 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInpatient Occupancy`
2025-08-19 09:50:55,372 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabABDM Settings`
2025-08-19 09:50:55,405 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDiagnosis`
2025-08-19 09:50:55,437 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDosage Strength`
2025-08-19 09:50:55,485 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabNursing Task`
2025-08-19 09:50:55,526 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDrug Prescription`
2025-08-19 09:50:55,559 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabMedication Class`
2025-08-19 09:50:55,595 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabOrganism Test Result`
2025-08-19 09:50:55,627 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabBody Part`
2025-08-19 09:50:55,657 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExercise Difficulty Level`
2025-08-19 09:50:55,696 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInpatient Medication Order Entry`
2025-08-19 09:50:55,728 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabDescriptive Test Result`
2025-08-19 09:50:55,771 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabService Request Category`
2025-08-19 09:50:55,810 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabTherapy Plan Template`
2025-08-19 09:50:55,843 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabExercise Type`
2025-08-19 09:50:55,874 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabNursing Checklist Template`
2025-08-19 09:50:55,913 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInpatient Medication Order`
2025-08-19 09:50:55,952 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSpecimen`
2025-08-19 09:50:55,990 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabLab Prescription`
2025-08-19 09:50:56,025 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabInpatient Medication Entry Detail`
2025-08-19 09:50:56,058 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient Assessment Parameter`
2025-08-19 09:50:56,088 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabSample Type`
2025-08-19 09:50:56,115 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabMedication Ingredient`
2025-08-19 09:50:56,144 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabPatient History Custom Document Type`
2025-08-19 09:50:56,174 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabCodification Table`
2025-08-19 09:51:25,396 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_name` varchar(140) unique,
`scale_min` int(11) not null default 0,
`scale_max` int(11) not null default 0,
`assessment_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:51:25,623 WARNING database DDL Query made to DB:
create table `tabProcedure Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`procedure` varchar(140),
`procedure_name` varchar(140),
`department` varchar(140),
`practitioner` varchar(140),
`service_request` varchar(140),
`date` date,
`comments` varchar(140),
`appointment_booked` int(1) not null default 0,
`procedure_created` int(1) not null default 0,
`invoiced` int(1) not null default 0,
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
index `appointment_booked`(`appointment_booked`),
index `procedure_created`(`procedure_created`),
index `invoiced`(`invoiced`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:51:25,745 WARNING database DDL Query made to DB:
create table `tabPatient Appointment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`status` varchar(140),
`appointment_type` varchar(140),
`appointment_for` varchar(140),
`company` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`department` varchar(140),
`service_unit` varchar(140),
`appointment_date` date,
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`patient_sex` varchar(140),
`patient_age` varchar(140),
`duration` int(11) not null default 0,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`service_request` varchar(140),
`procedure_template` varchar(140),
`procedure_prescription` varchar(140),
`therapy_plan` varchar(140),
`therapy_type` varchar(140),
`appointment_time` time(6),
`appointment_datetime` datetime(6),
`add_video_conferencing` int(1) not null default 0,
`event` varchar(140),
`google_meet_link` varchar(140),
`mode_of_payment` varchar(140),
`billing_item` varchar(140),
`invoiced` int(1) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`ref_sales_invoice` varchar(140),
`referring_practitioner` varchar(140),
`position_in_queue` int(11) not null default 0,
`appointment_based_on_check_in` int(1) not null default 0,
`reminded` int(1) not null default 0,
`notes` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index `practitioner`(`practitioner`),
index `department`(`department`),
index `appointment_date`(`appointment_date`),
index `patient`(`patient`),
index `service_request`(`service_request`),
index `appointment_datetime`(`appointment_datetime`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-19 09:51:25,908 WARNING database DDL Query made to DB:
create table `tabHealthcare Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`practitioner_name` varchar(140),
`gender` varchar(140),
`image` text,
`status` varchar(140) default 'Active',
`mobile_phone` varchar(140),
`residence_phone` varchar(140),
`office_phone` varchar(140),
`practitioner_type` varchar(140) default 'Internal',
`employee` varchar(140),
`supplier` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`user_id` varchar(140),
`hospital` varchar(140),
`google_calendar` varchar(140),
`op_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
`default_currency` varchar(140),
`practitioner_primary_contact` varchar(140),
`mobile_no` varchar(140),
`email_id` varchar(140),
`practitioner_primary_address` varchar(140),
`primary_address` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `practitioner_name`(`practitioner_name`),
index `user_id`(`user_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
