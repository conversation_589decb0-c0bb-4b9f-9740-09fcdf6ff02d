2025-08-16 21:15:42,251 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {'args': '{"currency":"YER","country":"Yemen","timezone":"Asia/Aden","language":"English","full_name":"osama samomy","email":"<EMAIL>","password":"Osama@1998","company_name":"<PERSON><PERSON><PERSON><PERSON>","company_abbr":"A","chart_of_accounts":"Standard with Numbers","fy_start_date":"2025-01-01","fy_end_date":"2025-12-31","setup_demo":0}', 'cmd': 'frappe.desk.page.setup_wizard.setup_wizard.setup_complete'}
2025-08-17 10:46:18,883 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-17 10:47:48,893 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-17 10:49:19,594 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-17 10:57:07,156 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 10:57:07,342 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 10:57:07,344 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 11:02:03,330 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {'cmd': 'frappe.core.doctype.user.user.reset_password', 'user': '<EMAIL>'}
2025-08-17 12:21:21,467 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 12:21:22,704 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 12:21:22,706 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 13:00:24,752 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 13:00:24,758 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 13:00:27,454 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 14:00:34,038 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 14:00:34,047 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 14:00:34,215 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 15:00:42,461 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 15:00:43,411 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 15:00:43,413 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 16:00:50,388 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 16:00:50,397 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 16:00:50,728 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 17:00:58,130 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 17:00:58,139 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 17:00:58,467 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 18:00:06,654 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 18:00:06,663 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 18:00:07,487 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 19:00:14,626 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 19:00:14,635 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 19:00:15,349 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 20:00:21,512 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 20:00:21,706 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 20:00:21,709 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 21:00:29,986 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 21:00:30,282 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 21:00:30,284 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 22:00:37,954 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 22:00:37,962 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-17 22:00:38,267 ERROR frappe Unable to load translations
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 174, in get_all_translations
    return frappe.cache.hget(MERGED_TRANSLATION_KEY, lang, generator=_merge_translations)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/redis_wrapper.py", line 241, in hget
    value = generator()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 162, in _merge_translations
    all_translations.update(get_translations_from_apps(lang))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 192, in get_translations_from_apps
    translations.update(get_translations_from_csv(lang, app) or {})
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/translate.py", line 204, in get_translations_from_csv
    os.path.join(frappe.get_app_path(app, "translations"), lang + ".csv"), lang, app
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'my_theme'
2025-08-19 09:35:31,303 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'healthcare'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 09:35:59,149 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'healthcare'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 09:44:24,167 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'healthcare'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:11:12,042 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:12:22,180 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:20:53,464 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:24:07,545 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:26:29,929 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:41:08,633 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:41:30,494 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
