2025-08-20 10:13:34,421 WARNING database DDL Query made to DB:
create table `tabCode Value` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code_system` varchar(140),
`system_uri` varchar(140),
`experimental` int(1) not null default 0,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`code_value` varchar(140),
`value_set` varchar(140),
`display` text,
`status` varchar(140),
`version` varchar(140),
`level` int(10) not null default 0,
`definition` text,
`official_url` varchar(140),
`canonical_mapping` varchar(140),
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `code_system`(`code_system`),
index `system_uri`(`system_uri`),
index `code_value`(`code_value`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:34,537 WARNING database DDL Query made to DB:
create table `tabComplaint` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`complaints` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:34,772 WARNING database DDL Query made to DB:
create table `tabLab Test` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`template` varchar(140),
`lab_test_name` varchar(140),
`lab_test_group` varchar(140),
`department` varchar(140),
`company` varchar(140),
`status` varchar(140),
`date` date,
`time` time(6),
`submitted_date` datetime(6),
`result_date` date,
`approved_date` datetime(6),
`expected_result_date` date,
`expected_result_time` time(6),
`printed_on` datetime(6),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`patient_sex` varchar(140),
`inpatient_record` varchar(140),
`report_preference` varchar(140),
`email` varchar(140),
`mobile` varchar(140),
`service_unit` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`requesting_department` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`employee_designation` varchar(140),
`user` varchar(140),
`sample` varchar(140),
`descriptive_result` longtext,
`lab_test_comment` text,
`custom_result` longtext,
`worksheet_instructions` longtext,
`legend_print_position` varchar(140),
`result_legend` longtext,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`email_sent` int(1) not null default 0,
`sms_sent` int(1) not null default 0,
`printed` int(1) not null default 0,
`normal_toggle` int(1) not null default 0,
`imaging_toggle` int(1) not null default 0,
`descriptive_toggle` int(1) not null default 0,
`sensitivity_toggle` int(1) not null default 0,
`amended_from` varchar(140),
`prescription` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `lab_test_name`(`lab_test_name`),
index `department`(`department`),
index `status`(`status`),
index `result_date`(`result_date`),
index `patient`(`patient`),
index `mobile`(`mobile`),
index `practitioner`(`practitioner`),
index `service_request`(`service_request`),
index `invoiced`(`invoiced`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:34,916 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`plan_name` varchar(140) unique,
`link_existing_item` int(1) not null default 0,
`linked_item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`description` text,
`total_sessions` int(11) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,015 WARNING database DDL Query made to DB:
create table `tabObservation Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`abbr` varchar(140),
`condition` longtext,
`based_on_formula` int(1) not null default 0,
`formula` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,089 WARNING database DDL Query made to DB:
create table `tabSample Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`sample_type` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,196 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`title` varchar(140) unique,
`department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,281 WARNING database DDL Query made to DB:
create table `tabMedication Ingredient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,384 WARNING database DDL Query made to DB:
create table `tabFee Validity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
`patient` varchar(140),
`medical_department` varchar(140),
`status` varchar(140),
`patient_appointment` varchar(140),
`sales_invoice_ref` varchar(140),
`max_visits` int(11) not null default 0,
`visited` int(11) not null default 0,
`start_date` date,
`valid_till` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `practitioner`(`practitioner`),
index `patient`(`patient`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,486 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_parameter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,570 WARNING database DDL Query made to DB:
create table `tabMedication Class` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication_class` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,663 WARNING database DDL Query made to DB:
create table `tabClinical Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`clinical_note_type` varchar(140),
`terms_and_conditions` varchar(140),
`posting_date` datetime(6),
`practitioner` varchar(140),
`user` varchar(140),
`note` longtext,
`reference_doc` varchar(140),
`reference_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,760 WARNING database DDL Query made to DB:
create table `tabPatient Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`therapy_session` varchar(140),
`patient` varchar(140),
`assessment_template` varchar(140),
`company` varchar(140),
`healthcare_practitioner` varchar(140),
`assessment_datetime` datetime(6),
`assessment_description` text,
`total_score_obtained` int(11) not null default 0,
`total_score` int(11) not null default 0,
`scale_min` int(11) not null default 0,
`scale_max` int(11) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:35,957 WARNING database DDL Query made to DB:
create table `tabDescriptive Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_particulars` varchar(140),
`result_value` text,
`allow_blank` int(1) not null default 1,
`template` varchar(140),
`require_result_value` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,046 WARNING database DDL Query made to DB:
create table `tabExercise Difficulty Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`difficulty_level` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,240 WARNING database DDL Query made to DB:
create table `tabTherapy Session` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`appointment` varchar(140),
`therapy_plan` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`gender` varchar(140),
`company` varchar(140),
`therapy_type` varchar(140),
`practitioner` varchar(140),
`department` varchar(140),
`duration` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`location` varchar(140),
`service_unit` varchar(140),
`start_date` date,
`start_time` time(6),
`total_counts_targeted` int(11) not null default 0,
`total_counts_completed` int(11) not null default 0,
`service_request` varchar(140),
`invoiced` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,374 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient_encounter` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`patient_age` varchar(140),
`inpatient_record` varchar(140),
`company` varchar(140),
`status` varchar(140),
`practitioner` varchar(140),
`start_date` date,
`end_date` date,
`total_orders` decimal(21,9) not null default 0,
`completed_orders` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,499 WARNING database DDL Query made to DB:
create table `tabDrug Prescription` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`medication` varchar(140),
`drug_code` varchar(140),
`drug_name` varchar(140),
`strength` decimal(21,9) not null default 0,
`strength_uom` varchar(140),
`dosage_form` varchar(140),
`dosage_by_interval` int(1) not null default 0,
`dosage` varchar(140),
`interval` int(11) not null default 0,
`interval_uom` varchar(140),
`period` varchar(140),
`number_of_repeats_allowed` decimal(21,9) not null default 0,
`intent` varchar(140),
`priority` varchar(140),
`medication_request` varchar(140),
`comment` text,
`update_schedule` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,577 WARNING database DDL Query made to DB:
create table `tabLab Test UOM` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_uom` varchar(140) unique,
`uom_description` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,668 WARNING database DDL Query made to DB:
create table `tabNursing Checklist Template Task` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140),
`mandatory` int(1) not null default 0,
`type` varchar(140),
`time_offset` decimal(21,9),
`description` text,
`task_duration` decimal(21,9),
`task_doctype` varchar(140),
`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,743 WARNING database DDL Query made to DB:
create table `tabPatient Encounter Symptom` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`complaint` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,841 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Entry Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`service_unit` varchar(140),
`datetime` datetime(6),
`drug_code` varchar(140),
`drug_name` varchar(140),
`dosage` decimal(21,9) not null default 0,
`available_qty` decimal(21,9) not null default 0,
`dosage_form` varchar(140),
`instructions` text,
`against_imo` varchar(140),
`against_imoe` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,913 WARNING database DDL Query made to DB:
create table `tabFee Validity Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:36,992 WARNING database DDL Query made to DB:
create table `tabTreatment Plan Template Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`template` varchar(140),
`qty` int(11) not null default 1,
`instructions` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,092 WARNING database DDL Query made to DB:
create table `tabTherapy Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`therapy_type` varchar(140),
`no_of_sessions` int(11) not null default 0,
`sessions_completed` int(11) not null default 0,
`service_request` varchar(140),
`patient_care_type` varchar(140),
`intent` varchar(140),
`priority` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,264 WARNING database DDL Query made to DB:
create table `tabObservation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`observation_template` varchar(140),
`observation_category` varchar(140),
`company` varchar(140),
`posting_date` date,
`status` varchar(140) default 'Registered',
`medical_department` varchar(140),
`amended_from` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`age` varchar(140),
`gender` varchar(140),
`healthcare_practitioner` varchar(140),
`practitioner_name` varchar(140),
`has_component` int(1) not null default 0,
`preferred_display_name` varchar(140),
`sample_collection_required` int(1) not null default 0,
`permitted_unit` varchar(140),
`sample` varchar(140),
`sample_type` varchar(140),
`permitted_data_type` varchar(140),
`method` varchar(140),
`specimen` varchar(140),
`sample_collection_time` datetime(6),
`sample_status` varchar(140),
`result_template` varchar(140),
`result_attach` text,
`result_boolean` varchar(140),
`result_data` varchar(140),
`result_text` longtext,
`result_float` decimal(21,9) not null default 0,
`result_select` varchar(140),
`result_datetime` datetime(6),
`result_time` datetime(6),
`result_period_from` datetime(6),
`result_period_to` datetime(6),
`options` text,
`time_of_result` datetime(6),
`time_of_approval` datetime(6),
`interpretation_template` varchar(140),
`result_interpretation` longtext,
`observation_method` varchar(140),
`reference` text,
`note` longtext,
`description` longtext,
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`sales_invoice` varchar(140),
`sales_invoice_status` varchar(140),
`sales_invoice_item` varchar(140),
`service_request` varchar(140),
`disapproval_reason` text,
`parent_observation` varchar(140),
`observation_idx` int(11) not null default 0,
`days` int(11) not null default 0,
`invoiced` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,458 WARNING database DDL Query made to DB:
create table `tabBody Part Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`body_part` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,539 WARNING database DDL Query made to DB:
create table `tabPractitioner Service Unit Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`schedule` varchar(140),
`service_unit` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,633 WARNING database DDL Query made to DB:
create table `tabNormal Test Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lab_test_event` varchar(140),
`allow_blank` int(1) not null default 0,
`lab_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`normal_range` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,706 WARNING database DDL Query made to DB:
create table `tabPatient Encounter Diagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`diagnosis` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,801 WARNING database DDL Query made to DB:
create table `tabDiagnostic Report` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`age` varchar(140),
`practitioner` varchar(140),
`practitioner_name` varchar(140),
`company` varchar(140),
`status` varchar(140),
`naming_series` varchar(140),
`ref_doctype` varchar(140),
`docname` varchar(140),
`reference_posting_date` date,
`sample_collection` varchar(140),
`amended_from` varchar(140),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `sample_collection`(`sample_collection`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,881 WARNING database DDL Query made to DB:
create table `tabOrganism Test Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140),
`colony_population` text,
`colony_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:37,955 WARNING database DDL Query made to DB:
create table `tabPatient Assessment Parameter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_parameter` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:38,051 WARNING database DDL Query made to DB:
create table `tabPractitioner Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`allow_video_conferencing` int(1) not null default 0,
`schedule_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:38,219 WARNING database DDL Query made to DB:
create table `tabInpatient Record` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`blood_group` varchar(140),
`dob` date,
`mobile` varchar(140),
`email` varchar(140),
`phone` varchar(140),
`company` varchar(140),
`status` varchar(140) default 'Admission Scheduled',
`scheduled_date` date,
`admitted_datetime` datetime(6),
`expected_discharge` date,
`admission_encounter` varchar(140),
`admission_practitioner` varchar(140),
`medical_department` varchar(140),
`admission_ordered_for` date,
`admission_service_unit_type` varchar(140),
`admission_nursing_checklist_template` varchar(140),
`discharge_nursing_checklist_template` varchar(140),
`expected_length_of_stay` int(11) not null default 0,
`primary_practitioner` varchar(140),
`secondary_practitioner` varchar(140),
`admission_instruction` text,
`therapy_plan` varchar(140),
`discharge_ordered_date` date,
`discharge_practitioner` varchar(140),
`discharge_encounter` varchar(140),
`discharge_datetime` datetime(6),
`discharge_instructions` text,
`followup_date` date,
`discharge_note` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:38,371 WARNING database DDL Query made to DB:
create table `tabTherapy Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`therapy_type` varchar(140) unique,
`medical_department` varchar(140),
`description` text,
`default_duration` int(11) not null default 0,
`healthcare_service_unit` varchar(140),
`nursing_checklist_template` varchar(140),
`item` varchar(140),
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:38,514 WARNING database DDL Query made to DB:
create table `tabCode System` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`code_system` varchar(140) unique,
`uri` varchar(140),
`description` text,
`status` varchar(140),
`version` varchar(140),
`is_fhir_defined` int(1) not null default 1,
`oid` varchar(140) unique,
`experimental` int(1) not null default 1,
`immutable` int(1) not null default 0,
`complete` int(1) not null default 0,
`custom` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `disabled`(`disabled`),
index `version`(`version`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:38,650 WARNING database DDL Query made to DB:
create table `tabOrganism Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140),
`colony_population` text,
`colony_uom` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:38,759 WARNING database DDL Query made to DB:
create sequence if not exists observation_sample_collection_id_seq nocache nocycle
2025-08-20 10:13:38,766 WARNING database DDL Query made to DB:
create table `tabObservation Sample Collection` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation_template` varchar(140),
`has_component` int(1) not null default 0,
`sample` varchar(140) default 'Urine',
`sample_type` varchar(140),
`uom` varchar(140),
`status` varchar(140),
`container_closure_color` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`medical_department` varchar(140),
`collection_date_time` datetime(6),
`collection_point` varchar(140),
`collected_user` varchar(140),
`collected_by` varchar(140),
`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`reference_child` varchar(140),
`service_request` varchar(140),
`specimen` varchar(140),
`component_observation_parent` varchar(140),
`component_observations` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:38,924 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140) unique,
`medical_department` varchar(140),
`description` text,
`disabled` int(1) not null default 0,
`pre_op_nursing_checklist_template` varchar(140),
`post_op_nursing_checklist_template` varchar(140),
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`is_billable` int(1) not null default 0,
`rate` decimal(21,9) not null default 0,
`consume_stock` int(1) not null default 0,
`sample_uom` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`sample` varchar(140),
`sample_details` text,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `consume_stock`(`consume_stock`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,064 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Unit` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`healthcare_service_unit_name` varchar(140),
`is_group` int(1) not null default 0,
`service_unit_type` varchar(140),
`allow_appointments` int(1) not null default 0,
`overlap_appointments` int(1) not null default 0,
`service_unit_capacity` int(11) not null default 0,
`inpatient_occupancy` int(1) not null default 0,
`occupancy_status` varchar(140),
`company` varchar(140),
`warehouse` varchar(140),
`parent_healthcare_service_unit` varchar(140),
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `inpatient_occupancy`(`inpatient_occupancy`),
index `company`(`company`),
index `lft`(`lft`),
index `rgt`(`rgt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,138 WARNING database DDL Query made to DB:
alter table `tabHealthcare Service Unit`
					add unique `unique_service_unit_company`(healthcare_service_unit_name, company)
2025-08-20 10:13:39,192 WARNING database DDL Query made to DB:
create table `tabInpatient Medication Order Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`drug` varchar(140),
`drug_name` varchar(140),
`dosage` decimal(21,9) not null default 0,
`dosage_form` varchar(140),
`instructions` text,
`date` date,
`time` time(6),
`is_completed` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,277 WARNING database DDL Query made to DB:
create table `tabLab Test Group Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_or_new_line` varchar(140) default 'Add Test',
`lab_test_template` varchar(140),
`lab_test_rate` decimal(21,9) not null default 0,
`lab_test_description` varchar(140),
`group_event` varchar(140),
`group_test_uom` varchar(140),
`secondary_uom` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`allow_blank` int(1) not null default 0,
`group_test_normal_range` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,454 WARNING database DDL Query made to DB:
create table `tabObservation Reference Range` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`permitted_data_type` varchar(140),
`reference_type` varchar(140),
`applies_to` varchar(140),
`age` varchar(140),
`gestational_age` decimal(21,9) not null default 0,
`from_age_type` varchar(140) default 'Years',
`age_from` varchar(140),
`to_age_type` varchar(140) default 'Years',
`age_to` varchar(140),
`reference_from` varchar(140),
`datetime` datetime(6),
`from_duration` decimal(21,9),
`boolean_value` varchar(140),
`ratio` varchar(140),
`options` varchar(140),
`from_datetime` datetime(6),
`conditions` longtext,
`reference_to` varchar(140),
`to_datetime` datetime(6),
`to_duration` decimal(21,9),
`short_interpretation` varchar(140),
`long_interpretation` text,
`normal_boolean_value` varchar(140),
`normal_ratio` varchar(140),
`normal_from` decimal(21,9) not null default 0,
`normal_select` varchar(140),
`normal_condition` longtext,
`normal_interpretation` varchar(140),
`normal_to` decimal(21,9) not null default 0,
`normal_long_interpretation` text,
`abnormal_boolean_value` varchar(140),
`abnormal_from` decimal(21,9) not null default 0,
`abnormal_ratio` varchar(140),
`abnormal_select` varchar(140),
`abnormal_condition` longtext,
`abnormal_interpretation` varchar(140),
`abnormal_to` decimal(21,9) not null default 0,
`abnormal_long_interpretation` text,
`critical_boolean_value` varchar(140),
`critical_from` decimal(21,9) not null default 0,
`critical_ratio` varchar(140),
`critical_select` varchar(140),
`critical_condition` longtext,
`critical_interpretation` varchar(140),
`critical_to` decimal(21,9) not null default 0,
`critical_long_interpretation` text,
`reference_text` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,544 WARNING database DDL Query made to DB:
create table `tabService Request Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140) unique,
`patient_care_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_care_type`(`patient_care_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,675 WARNING database DDL Query made to DB:
create table `tabAppointment Type Service Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140),
`dn` varchar(140),
`op_consulting_charge_item` varchar(140),
`op_consulting_charge` decimal(21,9) not null default 0,
`inpatient_visit_charge_item` varchar(140),
`inpatient_visit_charge` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,772 WARNING database DDL Query made to DB:
create table `tabHealthcare Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`activity` varchar(140) unique,
`description` text,
`activity_duration` decimal(21,9),
`role` varchar(140),
`task_doctype` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,865 WARNING database DDL Query made to DB:
create table `tabClinical Procedure Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`barcode` varchar(140),
`uom` varchar(140),
`invoice_separately_as_consumables` int(1) not null default 0,
`batch_no` varchar(140),
`conversion_factor` decimal(21,9) not null default 0,
`stock_uom` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`actual_qty` decimal(21,9) not null default 0,
index `item_code`(`item_code`),
index `actual_qty`(`actual_qty`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:39,952 WARNING database DDL Query made to DB:
create table `tabExercise` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise_type` varchar(140),
`difficulty_level` varchar(140),
`counts_target` int(11) not null default 0,
`counts_completed` int(11) not null default 0,
`assistance_level` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:40,036 WARNING database DDL Query made to DB:
create table `tabSensitivity Test Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`antibiotic` varchar(140),
`antibiotic_sensitivity` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:40,123 WARNING database DDL Query made to DB:
create table `tabOrganism` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organism` varchar(140) unique,
`abbr` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:40,222 WARNING database DDL Query made to DB:
create table `tabPrescription Duration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number` int(11) not null default 0,
`period` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:40,406 WARNING database DDL Query made to DB:
create table `tabPatient` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`patient_name` varchar(140),
`sex` varchar(140),
`blood_group` varchar(140),
`dob` date,
`image` text,
`status` varchar(140),
`uid` varchar(140) unique,
`inpatient_record` varchar(140),
`inpatient_status` varchar(140),
`report_preference` varchar(140),
`mobile` varchar(140),
`phone` varchar(140),
`email` varchar(140),
`invite_user` int(1) not null default 1,
`user_id` varchar(140),
`customer` varchar(140),
`customer_group` varchar(140),
`territory` varchar(140),
`default_currency` varchar(140),
`default_price_list` varchar(140),
`language` varchar(140),
`patient_details` text,
`occupation` varchar(140),
`marital_status` varchar(140),
`allergies` text,
`medication` text,
`medical_history` text,
`surgical_history` text,
`tobacco_past_use` varchar(140),
`tobacco_current_use` varchar(140),
`alcohol_past_use` varchar(140),
`alcohol_current_use` varchar(140),
`surrounding_factors` text,
`other_risk_factors` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient_name`(`patient_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:40,559 WARNING database DDL Query made to DB:
create table `tabAppointment Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`appointment_type` varchar(140) unique,
`default_duration` int(11) not null default 0,
`allow_booking_for` varchar(140) default 'Practitioner',
`color` varchar(140),
`price_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:40,696 WARNING database DDL Query made to DB:
create table `tabVital Signs` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`title` varchar(140),
`patient` varchar(140),
`patient_name` varchar(140),
`inpatient_record` varchar(140),
`appointment` varchar(140),
`encounter` varchar(140),
`company` varchar(140),
`signs_date` date,
`signs_time` time(6),
`temperature` varchar(140),
`pulse` varchar(140),
`respiratory_rate` varchar(140),
`tongue` varchar(140),
`abdomen` varchar(140),
`reflexes` varchar(140),
`bp_systolic` varchar(140),
`bp_diastolic` varchar(140),
`bp` varchar(140),
`vital_signs_note` text,
`height` decimal(21,9) not null default 0,
`weight` decimal(21,9) not null default 0,
`bmi` decimal(21,9) not null default 0,
`nutrition_note` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:40,861 WARNING database DDL Query made to DB:
create table `tabObservation Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`observation` varchar(140) unique,
`observation_category` varchar(140),
`preferred_display_name` varchar(140),
`abbr` varchar(140),
`has_component` int(1) not null default 0,
`medical_department` varchar(140),
`description` longtext,
`method` varchar(140),
`method_value` varchar(140),
`service_unit` varchar(140),
`result_template` varchar(140),
`interpretation_template` varchar(140),
`permitted_data_type` varchar(140),
`permitted_unit` varchar(140),
`options` text,
`template` varchar(140),
`sample_collection_required` int(1) not null default 0,
`sample` varchar(140),
`sample_qty` decimal(21,9) not null default 0,
`uom` varchar(140),
`sample_type` varchar(140),
`container_closure_color` varchar(140),
`sample_details` text,
`is_billable` int(1) not null default 0,
`link_existing_item` int(1) not null default 0,
`item` varchar(140),
`item_code` varchar(140),
`item_group` varchar(140),
`rate` decimal(21,9) not null default 0,
`change_in_item` int(1) not null default 0,
`patient_care_type` varchar(140),
`staff_role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_billable`(`is_billable`),
index `item`(`item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:40,939 WARNING database DDL Query made to DB:
create table `tabPatient Relation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`relation` varchar(140),
`description` text,
index `relation`(`relation`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:41,025 WARNING database DDL Query made to DB:
create table `tabExercise Type Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`image` text,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-20 10:13:46,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `patient` varchar(140), ADD COLUMN `patient_name` varchar(140), ADD COLUMN `ref_practitioner` varchar(140), ADD COLUMN `service_unit` varchar(140)
2025-08-20 10:13:46,166 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-08-20 10:13:46,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `patient` varchar(140), ADD COLUMN `inpatient_medication_entry_child` varchar(140)
2025-08-20 10:13:46,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-08-20 10:13:46,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `reference_dt` varchar(140), ADD COLUMN `reference_dn` varchar(140), ADD COLUMN `practitioner` varchar(140), ADD COLUMN `medical_department` varchar(140), ADD COLUMN `service_unit` varchar(140)
2025-08-20 10:13:46,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-08-20 10:13:46,309 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `creation`(`creation`)
2025-08-20 10:13:46,363 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `inpatient_medication_entry` varchar(140)
2025-08-20 10:13:46,369 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0
