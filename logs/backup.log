Please specify --site sitename
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250816_213004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-16 21:30:05.698523
Config  : ./erpnextapp/private/backups/20250816_213004-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250816_213004-erpnextapp-database.sql.gz         897.1KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250817_010008-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-17 01:00:13.441801
Config  : ./erpnextapp/private/backups/20250817_010008-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250817_010008-erpnextapp-database.sql.gz         941.9KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250817_070004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-17 07:00:06.051292
Config  : ./erpnextapp/private/backups/20250817_070004-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250817_070004-erpnextapp-database.sql.gz         949.5KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250817_130003-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-17 13:00:05.774045
Config  : ./erpnextapp/private/backups/20250817_130003-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250817_130003-erpnextapp-database.sql.gz         965.3KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250817_190004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-17 19:00:06.250001
Config  : ./erpnextapp/private/backups/20250817_190004-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250817_190004-erpnextapp-database.sql.gz         974.4KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250818_010004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-18 01:00:07.552289
Config  : ./erpnextapp/private/backups/20250818_010004-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250818_010004-erpnextapp-database.sql.gz         983.8KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250818_070004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-18 07:00:06.601905
Config  : ./erpnextapp/private/backups/20250818_070004-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250818_070004-erpnextapp-database.sql.gz         991.3KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250818_130004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-18 13:00:06.412885
Config  : ./erpnextapp/private/backups/20250818_130004-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250818_130004-erpnextapp-database.sql.gz         998.9KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250818_190004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-18 19:00:06.175831
Config  : ./erpnextapp/private/backups/20250818_190004-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250818_190004-erpnextapp-database.sql.gz         1006.3KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250819_010006-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-19 01:00:09.640180
Config  : ./erpnextapp/private/backups/20250819_010006-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250819_010006-erpnextapp-database.sql.gz         1010.8KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250819_070004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-19 07:00:06.273023
Config  : ./erpnextapp/private/backups/20250819_070004-erpnextapp-site_config_backup.json 158.0B
Database: ./erpnextapp/private/backups/20250819_070004-erpnextapp-database.sql.gz         1010.8KiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250819_130003-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-19 13:00:06.119556
Config  : ./erpnextapp/private/backups/20250819_130003-erpnextapp-site_config_backup.json 182.0B
Database: ./erpnextapp/private/backups/20250819_130003-erpnextapp-database.sql.gz         1.4MiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250819_190004-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-19 19:00:06.200263
Config  : ./erpnextapp/private/backups/20250819_190004-erpnextapp-site_config_backup.json 182.0B
Database: ./erpnextapp/private/backups/20250819_190004-erpnextapp-database.sql.gz         1.4MiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250820_010006-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-20 01:00:09.707907
Config  : ./erpnextapp/private/backups/20250820_010006-erpnextapp-site_config_backup.json 182.0B
Database: ./erpnextapp/private/backups/20250820_010006-erpnextapp-database.sql.gz         1.4MiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250820_070005-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-20 07:00:07.418033
Config  : ./erpnextapp/private/backups/20250820_070005-erpnextapp-site_config_backup.json 182.0B
Database: ./erpnextapp/private/backups/20250820_070005-erpnextapp-database.sql.gz         1.4MiB
Backup for Site erpnextapp has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_b259cfd948cee20e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _b259cfd948cee20e | /usr/bin/gzip >> ./erphealth/private/backups/20250820_130004-erphealth-database.sql.gz

Backup Summary for erphealth at 2025-08-20 13:00:06.134391
Config  : ./erphealth/private/backups/20250820_130004-erphealth-site_config_backup.json 94.0B
Database: ./erphealth/private/backups/20250820_130004-erphealth-database.sql.gz         905.3KiB
Backup for Site erphealth has been successfully completed
File ./erpnextapp/private/backups/20250820_010006-erpnextapp-site_config_backup.json is recent
File ./erpnextapp/private/backups/20250820_070005-erpnextapp-database.sql.gz is recent
File ./erpnextapp/private/backups/20250819_190004-erpnextapp-site_config_backup.json is recent
File ./erpnextapp/private/backups/20250820_070005-erpnextapp-site_config_backup.json is recent
File ./erpnextapp/private/backups/20250820_010006-erpnextapp-database.sql.gz is recent
File ./erpnextapp/private/backups/20250819_190004-erpnextapp-database.sql.gz is recent
set -o pipefail; /usr/bin/mariadb-dump --user=_f4e7e3fcc145ebb1 --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f4e7e3fcc145ebb1 | /usr/bin/gzip >> ./erpnextapp/private/backups/20250820_130006-erpnextapp-database.sql.gz

Backup Summary for erpnextapp at 2025-08-20 13:00:08.310648
Config  : ./erpnextapp/private/backups/20250820_130006-erpnextapp-site_config_backup.json 182.0B
Database: ./erpnextapp/private/backups/20250820_130006-erpnextapp-database.sql.gz         1.4MiB
Backup for Site erpnextapp has been successfully completed
