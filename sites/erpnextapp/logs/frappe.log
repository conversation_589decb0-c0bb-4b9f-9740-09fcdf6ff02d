2025-08-19 10:42:39,818 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:43:10,768 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:43:55,330 ERROR frappe Could not take error snapshot: 'NoneType' object has no attribute 'get'
Site: erpnextapp
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1004, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'vuexy_theme'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 71, in get_controller
    site_controllers[doctype] = import_controller(doctype)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 96, in import_controller
    module = load_doctype_module(doctype, module_name)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 248, in load_doctype_module
    app = get_module_app(module)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/modules/utils.py", line 271, in get_module_app
    app = frappe.local.module_app.get(scrub(module))
AttributeError: 'NoneType' object has no attribute 'get'
2025-08-19 10:48:40,733 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 10:49:32,595 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 10:53:09,104 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 10:56:18,231 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:13:25,446 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {'app_path': 'home'}
2025-08-19 11:14:42,113 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {'app_path': 'home'}
2025-08-19 11:15:22,293 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:18:06,802 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:19:26,371 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:19:30,338 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:19:38,807 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:19:43,676 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:20:44,808 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:24:09,872 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:27:03,982 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:27:16,158 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-19 11:27:20,400 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {'app_path': 'home'}
2025-08-19 11:29:48,226 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {'app_path': 'home'}
2025-08-20 09:43:58,872 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-20 09:57:05,187 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-20 09:58:02,050 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-20 10:01:19,543 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
2025-08-20 10:05:55,386 ERROR frappe New Exception collected in error log
Site: erpnextapp
Form Dict: {}
