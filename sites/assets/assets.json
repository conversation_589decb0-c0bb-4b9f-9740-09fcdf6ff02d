{"billing.bundle.js": "/assets/frappe/dist/js/billing.bundle.3M2NKQ7F.js", "bootstrap-4-web.bundle.js": "/assets/frappe/dist/js/bootstrap-4-web.bundle.AZ67VXZX.js", "controls.bundle.js": "/assets/frappe/dist/js/controls.bundle.NF7UCKIX.js", "data_import_tools.bundle.js": "/assets/frappe/dist/js/data_import_tools.bundle.FXJ6JQBT.js", "desk.bundle.js": "/assets/frappe/dist/js/desk.bundle.QI26FF7G.js", "dialog.bundle.js": "/assets/frappe/dist/js/dialog.bundle.ZK4CY7BD.js", "form.bundle.js": "/assets/frappe/dist/js/form.bundle.EIGSESZZ.js", "frappe-web.bundle.js": "/assets/frappe/dist/js/frappe-web.bundle.DO5AHJ5M.js", "libs.bundle.js": "/assets/frappe/dist/js/libs.bundle.LLRFRX7M.js", "list.bundle.js": "/assets/frappe/dist/js/list.bundle.IK4H5VVV.js", "logtypes.bundle.js": "/assets/frappe/dist/js/logtypes.bundle.MJKW7EK3.js", "onboarding_tours.bundle.js": "/assets/frappe/dist/js/onboarding_tours.bundle.P7QYMXLW.js", "report.bundle.js": "/assets/frappe/dist/js/report.bundle.6ZF73JDK.js", "sentry.bundle.js": "/assets/frappe/dist/js/sentry.bundle.SI3DB3BY.js", "telemetry.bundle.js": "/assets/frappe/dist/js/telemetry.bundle.ZJBT5ETW.js", "user_profile_controller.bundle.js": "/assets/frappe/dist/js/user_profile_controller.bundle.TAMQL3L3.js", "video_player.bundle.js": "/assets/frappe/dist/js/video_player.bundle.IOEIXC2G.js", "web_form.bundle.js": "/assets/frappe/dist/js/web_form.bundle.Y6B7WAQT.js", "form_builder.bundle.js": "/assets/frappe/dist/js/form_builder.bundle.AGMNK5PM.js", "print_format_builder.bundle.js": "/assets/frappe/dist/js/print_format_builder.bundle.Z2JKDXC5.js", "workflow_builder.bundle.js": "/assets/frappe/dist/js/workflow_builder.bundle.VWQMGINN.js", "build_events.bundle.js": "/assets/frappe/dist/js/build_events.bundle.Q7QTA36B.js", "file_uploader.bundle.js": "/assets/frappe/dist/js/file_uploader.bundle.4OMF6ZZO.js", "kanban_board.bundle.js": "/assets/frappe/dist/js/kanban_board.bundle.OUFA2R27.js", "desk.bundle.css": "/assets/frappe/dist/css/desk.bundle.4IGBC2VK.css", "email.bundle.css": "/assets/frappe/dist/css/email.bundle.JH3UWECE.css", "login.bundle.css": "/assets/frappe/dist/css/login.bundle.OKPCUIAJ.css", "print.bundle.css": "/assets/frappe/dist/css/print.bundle.426WNQWE.css", "print_format.bundle.css": "/assets/frappe/dist/css/print_format.bundle.QREJTLV5.css", "report.bundle.css": "/assets/frappe/dist/css/report.bundle.IBPSI3NP.css", "web_form.bundle.css": "/assets/frappe/dist/css/web_form.bundle.SBVX3XI2.css", "website.bundle.css": "/assets/frappe/dist/css/website.bundle.SOSWYXWI.css", "bank-reconciliation-tool.bundle.js": "/assets/erpnext/dist/js/bank-reconciliation-tool.bundle.G7MHMAFO.js", "erpnext-web.bundle.js": "/assets/erpnext/dist/js/erpnext-web.bundle.253I7LT4.js", "erpnext.bundle.js": "/assets/erpnext/dist/js/erpnext.bundle.WNGP2KIS.js", "item-dashboard.bundle.js": "/assets/erpnext/dist/js/item-dashboard.bundle.OKM5ADNP.js", "point-of-sale.bundle.js": "/assets/erpnext/dist/js/point-of-sale.bundle.LZTBK57Q.js", "bom_configurator.bundle.js": "/assets/erpnext/dist/js/bom_configurator.bundle.6NZSNVVE.js", "erpnext-web.bundle.css": "/assets/erpnext/dist/css/erpnext-web.bundle.Z5T2SFVB.css", "erpnext.bundle.css": "/assets/erpnext/dist/css/erpnext.bundle.KCCEUJYO.css", "erpnext_email.bundle.css": "/assets/erpnext/dist/css/erpnext_email.bundle.UU3TCFIB.css", "hierarchy-chart.bundle.js": "/assets/hrms/dist/js/hierarchy-chart.bundle.C6LCZVVS.js", "hrms.bundle.js": "/assets/hrms/dist/js/hrms.bundle.A6T6RA2E.js", "interview.bundle.js": "/assets/hrms/dist/js/interview.bundle.ZJTIMGTY.js", "performance.bundle.js": "/assets/hrms/dist/js/performance.bundle.HWRHZE2C.js", "hrms.bundle.css": "/assets/hrms/dist/css/hrms.bundle.3XBSASV4.css", "healthcare.bundle.js": "/assets/healthcare/dist/js/healthcare.bundle.J7NR6S7D.js"}