/**
 * Vuexy Theme - Standalone CSS
 * Complete Vuexy-inspired theme for ERPNext v15
 * No dependencies, works independently
 */

/* Import Cairo Font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    /* Vuexy Colors */
    --vuexy-primary: #7367f0;
    --vuexy-primary-light: #9c93f7;
    --vuexy-primary-dark: #5e57d9;
    --vuexy-secondary: #82868b;
    --vuexy-success: #28c76f;
    --vuexy-info: #00cfe8;
    --vuexy-warning: #ff9f43;
    --vuexy-danger: #ea5455;
    --vuexy-light: #f8f8f8;
    --vuexy-dark: #4b4b4b;
    
    /* Sidebar Colors */
    --vuexy-sidebar-bg: #283046;
    --vuexy-sidebar-text: #b4b7bd;
    --vuexy-sidebar-text-active: #ffffff;
    --vuexy-sidebar-border: #3b4253;
    --vuexy-sidebar-hover: #2f3349;
    
    /* Font */
    --vuexy-font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Base Styles */
html, body {
    font-family: var(--vuexy-font-family) !important;
    background-color: var(--vuexy-light) !important;
}

/* Apply Cairo font to all elements */
* {
    font-family: var(--vuexy-font-family) !important;
}

/* Sidebar Styling */
.layout-side-section,
.sidebar,
.desk-sidebar,
.layout-side-section .sidebar,
.layout-side-section .desk-sidebar {
    background-color: var(--vuexy-sidebar-bg) !important;
    color: var(--vuexy-sidebar-text) !important;
    border-right: none !important;
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    width: 260px !important;
    z-index: 1030 !important;
    transition: all 0.3s ease !important;
}

/* Sidebar Items */
.layout-side-section .sidebar-item,
.sidebar .sidebar-item,
.desk-sidebar .sidebar-item,
.layout-side-section a,
.sidebar a,
.desk-sidebar a {
    color: var(--vuexy-sidebar-text) !important;
    border-radius: 6px !important;
    margin: 2px 8px !important;
    padding: 8px 12px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
}

.layout-side-section .sidebar-item:hover,
.sidebar .sidebar-item:hover,
.desk-sidebar .sidebar-item:hover,
.layout-side-section a:hover,
.sidebar a:hover,
.desk-sidebar a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--vuexy-sidebar-text-active) !important;
}

.layout-side-section .sidebar-item.selected,
.sidebar .sidebar-item.selected,
.desk-sidebar .sidebar-item.selected,
.layout-side-section .active,
.sidebar .active,
.desk-sidebar .active {
    background-color: var(--vuexy-primary) !important;
    color: var(--vuexy-sidebar-text-active) !important;
    box-shadow: 0 3px 10px 0 rgba(115, 103, 240, 0.4) !important;
}

/* Main Content Area */
.layout-main-section {
    margin-left: 260px !important;
    transition: margin-left 0.3s ease !important;
    background-color: var(--vuexy-light) !important;
    min-height: 100vh !important;
}

/* Navbar Styling */
.navbar,
.nav-bar,
.desk-navbar,
.navbar-default {
    background-color: #ffffff !important;
    border-bottom: 1px solid #ebe9f1 !important;
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1) !important;
    height: 70px !important;
}

/* Button Styling */
.btn-primary {
    background-color: var(--vuexy-primary) !important;
    border-color: var(--vuexy-primary) !important;
    color: #ffffff !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    box-shadow: 0 3px 10px 0 rgba(115, 103, 240, 0.4) !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: var(--vuexy-primary-dark) !important;
    border-color: var(--vuexy-primary-dark) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 5px 15px 0 rgba(115, 103, 240, 0.5) !important;
}

.btn-secondary {
    background-color: var(--vuexy-secondary) !important;
    border-color: var(--vuexy-secondary) !important;
    color: #ffffff !important;
}

.btn-success {
    background-color: var(--vuexy-success) !important;
    border-color: var(--vuexy-success) !important;
}

.btn-info {
    background-color: var(--vuexy-info) !important;
    border-color: var(--vuexy-info) !important;
}

.btn-warning {
    background-color: var(--vuexy-warning) !important;
    border-color: var(--vuexy-warning) !important;
}

.btn-danger {
    background-color: var(--vuexy-danger) !important;
    border-color: var(--vuexy-danger) !important;
}

/* Card Styling */
.card,
.widget,
.dashboard-widget,
.frappe-card {
    border: 1px solid #ebe9f1 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.1) !important;
    transition: all 0.3s ease !important;
    background-color: #ffffff !important;
}

.card:hover,
.widget:hover,
.dashboard-widget:hover,
.frappe-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 34px 0 rgba(39, 44, 49, 0.06), 0 2px 8px 0 rgba(39, 44, 49, 0.12) !important;
}

/* Form Styling */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
textarea,
select {
    border: 1px solid #d8d6de !important;
    border-radius: 6px !important;
    font-family: var(--vuexy-font-family) !important;
    transition: all 0.3s ease !important;
    padding: 8px 12px !important;
}

.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    border-color: var(--vuexy-primary) !important;
    box-shadow: 0 3px 10px 0 rgba(115, 103, 240, 0.1) !important;
    outline: none !important;
}

/* Table Styling */
.table th {
    font-weight: 600 !important;
    color: #5e5873 !important;
    border-bottom: 2px solid #ebe9f1 !important;
    background-color: #f8f8f8 !important;
}

.table-hover tbody tr:hover {
    background-color: rgba(115, 103, 240, 0.05) !important;
}

/* Alert Styling */
.alert-primary {
    background-color: rgba(115, 103, 240, 0.1) !important;
    border-color: rgba(115, 103, 240, 0.2) !important;
    color: var(--vuexy-primary-dark) !important;
}

.alert-success {
    background-color: rgba(40, 199, 111, 0.1) !important;
    border-color: rgba(40, 199, 111, 0.2) !important;
    color: #1f9954 !important;
}

.alert-info {
    background-color: rgba(0, 207, 232, 0.1) !important;
    border-color: rgba(0, 207, 232, 0.2) !important;
    color: #00a5ba !important;
}

.alert-warning {
    background-color: rgba(255, 159, 67, 0.1) !important;
    border-color: rgba(255, 159, 67, 0.2) !important;
    color: #e6842a !important;
}

.alert-danger {
    background-color: rgba(234, 84, 85, 0.1) !important;
    border-color: rgba(234, 84, 85, 0.2) !important;
    color: #d63031 !important;
}

/* Badge Styling */
.badge-primary {
    background-color: var(--vuexy-primary) !important;
    color: #ffffff !important;
}

.badge-success {
    background-color: var(--vuexy-success) !important;
    color: #ffffff !important;
}

.badge-info {
    background-color: var(--vuexy-info) !important;
    color: #ffffff !important;
}

.badge-warning {
    background-color: var(--vuexy-warning) !important;
    color: #ffffff !important;
}

.badge-danger {
    background-color: var(--vuexy-danger) !important;
    color: #ffffff !important;
}

/* Text Colors */
.text-primary { color: var(--vuexy-primary) !important; }
.text-secondary { color: var(--vuexy-secondary) !important; }
.text-success { color: var(--vuexy-success) !important; }
.text-info { color: var(--vuexy-info) !important; }
.text-warning { color: var(--vuexy-warning) !important; }
.text-danger { color: var(--vuexy-danger) !important; }

/* Background Colors */
.bg-primary { background-color: var(--vuexy-primary) !important; }
.bg-secondary { background-color: var(--vuexy-secondary) !important; }
.bg-success { background-color: var(--vuexy-success) !important; }
.bg-info { background-color: var(--vuexy-info) !important; }
.bg-warning { background-color: var(--vuexy-warning) !important; }
.bg-danger { background-color: var(--vuexy-danger) !important; }

/* Login Page Styling */
.login-page,
.web-form-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh !important;
}

.login-container,
.login-box,
.web-form-wrapper {
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 12px !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(10px) !important;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f4f4f4;
}

::-webkit-scrollbar-thumb {
    background: var(--vuexy-secondary);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--vuexy-primary);
}

/* Selection */
::selection {
    background-color: rgba(115, 103, 240, 0.2);
    color: var(--vuexy-primary-dark);
}

/* Responsive */
@media (max-width: 768px) {
    .layout-side-section,
    .sidebar,
    .desk-sidebar {
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
    }
    
    .layout-main-section {
        margin-left: 0 !important;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card,
.widget,
.dashboard-widget,
.frappe-card {
    animation: fadeIn 0.3s ease-out;
}

/* Focus Styles */
*:focus {
    outline: 2px solid rgba(115, 103, 240, 0.3) !important;
    outline-offset: 2px !important;
}

/* Additional ERPNext specific styling */
.page-head {
    background-color: #ffffff !important;
    border-bottom: 1px solid #ebe9f1 !important;
}

.page-title {
    color: #5e5873 !important;
    font-weight: 600 !important;
}

.list-row {
    border-bottom: 1px solid #ebe9f1 !important;
}

.list-row:hover {
    background-color: rgba(115, 103, 240, 0.05) !important;
}

/* Module icons */
.module-icon {
    background-color: var(--vuexy-primary) !important;
    color: #ffffff !important;
    border-radius: 8px !important;
}

/* Workspace styling */
.workspace-sidebar {
    background-color: var(--vuexy-sidebar-bg) !important;
}

.workspace-sidebar .sidebar-item {
    color: var(--vuexy-sidebar-text) !important;
}

.workspace-sidebar .sidebar-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--vuexy-sidebar-text-active) !important;
}
