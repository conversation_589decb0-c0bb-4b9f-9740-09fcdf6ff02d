Metadata-Version: 2.4
Name: vuexy_theme
Version: 0.0.1
Summary: Modern Vuexy-inspired admin theme for ERPNext v15
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/markdown

### Vuexy Theme

Modern Vuexy-inspired admin theme for ERPNext v15

### Installation

You can install this app using the [bench](https://github.com/frappe/bench) CLI:

```bash
cd $PATH_TO_YOUR_BENCH
bench get-app $URL_OF_THIS_REPO --branch develop
bench install-app vuexy_theme
```

### Contributing

This app uses `pre-commit` for code formatting and linting. Please [install pre-commit](https://pre-commit.com/#installation) and enable it for this repository:

```bash
cd apps/vuexy_theme
pre-commit install
```

Pre-commit is configured to use the following tools for checking and formatting your code:

- ruff
- eslint
- prettier
- pyupgrade

### License

mit

